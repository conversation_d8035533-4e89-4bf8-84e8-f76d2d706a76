const axios = require('axios');

async function testRawResponsesDisplay() {
    console.log('🔍 Testing Raw LLM Responses Display');
    console.log('===================================\n');
    
    const baseUrl = 'http://localhost:3000';
    
    // Test with mock LLM to ensure we get responses
    console.log('1. Testing analysis with mock LLM...');
    try {
        const response = await axios.post(`${baseUrl}/api/advanced-analyze`, {
            doc_id: 'test_raw_001',
            title: 'Test Document for Raw Responses',
            text: 'The FTC announced new regulations targeting Big Tech companies like Google and Meta.',
            useRealLLM: false // Use mock for testing
        });
        
        console.log('   Response status:', response.status);
        console.log('   Response data keys:', Object.keys(response.data));

        if (response.data.analysis) {
            console.log('   ✅ Analysis completed successfully');
            
            const analysis = response.data.analysis;
            
            // Check actors raw response
            if (analysis.actors && analysis.actors._rawLLMResponse) {
                console.log('   ✅ Actors raw response captured');
                console.log(`   Actors prompt length: ${analysis.actors._prompt?.length || 0} characters`);
                console.log(`   Actors response length: ${analysis.actors._rawLLMResponse.length} characters`);
            } else {
                console.log('   ❌ Actors raw response not captured');
            }
            
            // Check relationships raw response
            if (analysis.relationships && analysis.relationships._rawLLMResponse) {
                console.log('   ✅ Relationships raw response captured');
                console.log(`   Relationships prompt length: ${analysis.relationships._prompt?.length || 0} characters`);
                console.log(`   Relationships response length: ${analysis.relationships._rawLLMResponse.length} characters`);
            } else {
                console.log('   ❌ Relationships raw response not captured');
            }
            
            // Check portrayals raw response
            if (analysis.portrayals && analysis.portrayals._rawLLMResponse) {
                console.log('   ✅ Portrayals raw response captured');
                console.log(`   Portrayals prompt length: ${analysis.portrayals._prompt?.length || 0} characters`);
                console.log(`   Portrayals response length: ${analysis.portrayals._rawLLMResponse.length} characters`);
            } else {
                console.log('   ❌ Portrayals raw response not captured');
            }
            
            // Check issue scope raw response
            if (analysis.issue_scope && analysis.issue_scope._rawLLMResponse) {
                console.log('   ✅ Issue scope raw response captured');
                console.log(`   Issue scope prompt length: ${analysis.issue_scope._prompt?.length || 0} characters`);
                console.log(`   Issue scope response length: ${analysis.issue_scope._rawLLMResponse.length} characters`);
            } else {
                console.log('   ❌ Issue scope raw response not captured');
            }
            
            // Check causal mechanisms raw response
            if (analysis.causal_mechanisms && analysis.causal_mechanisms._rawLLMResponse) {
                console.log('   ✅ Causal mechanisms raw response captured');
                console.log(`   Causal mechanisms prompt length: ${analysis.causal_mechanisms._prompt?.length || 0} characters`);
                console.log(`   Causal mechanisms response length: ${analysis.causal_mechanisms._rawLLMResponse.length} characters`);
            } else {
                console.log('   ❌ Causal mechanisms raw response not captured');
            }
            
            // Show sample of raw response
            if (analysis.actors && analysis.actors._rawLLMResponse) {
                console.log('\n📄 Sample Raw Response (Actors):');
                console.log('================================');
                const sample = analysis.actors._rawLLMResponse.substring(0, 200);
                console.log(sample + (analysis.actors._rawLLMResponse.length > 200 ? '...' : ''));
            }
            
            if (analysis.actors && analysis.actors._prompt) {
                console.log('\n📝 Sample Prompt (Actors):');
                console.log('=========================');
                const promptSample = analysis.actors._prompt.substring(0, 300);
                console.log(promptSample + (analysis.actors._prompt.length > 300 ? '...' : ''));
            }
            
        } else {
            console.log('   ❌ Analysis failed');
            console.log('   Response data:', response.data);
        }
        
    } catch (error) {
        console.log('   ❌ Analysis request failed:', error.message);
        if (error.response) {
            console.log('   Status:', error.response.status);
            console.log('   Error data:', error.response.data);
        }
    }
    
    console.log('\n📊 Raw Responses Test Summary');
    console.log('=============================');
    console.log('✅ Modified AdvancedDocumentAnalyzer to store raw responses');
    console.log('✅ Updated analyze method to include raw response data');
    console.log('✅ Fixed data structure to preserve raw responses through JSON serialization');
    
    console.log('\n🎯 Frontend Display:');
    console.log('The Raw LLM Responses tab should now show:');
    console.log('- Complete prompts used for each analysis step');
    console.log('- Full unprocessed LLM responses');
    console.log('- Proper formatting and syntax highlighting');
    
    console.log('\n🚀 Test the web interface at http://localhost:3000');
    console.log('Navigate to the "Raw LLM Responses" tab after running an analysis!');
}

// Run the test
testRawResponsesDisplay().catch(console.error);
