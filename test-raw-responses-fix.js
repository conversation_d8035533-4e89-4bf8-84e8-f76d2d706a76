const axios = require('axios');

async function testRawResponsesFix() {
    console.log('🔧 Testing Raw LLM Responses Fix');
    console.log('===============================\n');
    
    const baseUrl = 'http://localhost:3000';
    
    // Test with mock LLM to ensure we get proper responses
    console.log('1. Testing analysis with mock LLM...');
    try {
        const response = await axios.post(`${baseUrl}/api/advanced-analyze`, {
            doc_id: 'test_fix_001',
            title: 'Test Document for Raw Responses Fix',
            text: 'The FTC announced new regulations targeting Big Tech companies like Google and Meta.',
            useRealLLM: false
        });
        
        if (response.data.analysis) {
            console.log('   ✅ Analysis completed successfully');
            
            const analysis = response.data.analysis;
            
            // Check actors
            console.log(`   Actors found: ${analysis.actors.length}`);
            if (analysis.actors._rawLLMResponse) {
                console.log('   ✅ Actors raw response captured');
                console.log(`   Actors response length: ${analysis.actors._rawLLMResponse.length} characters`);
                
                // Check if response contains proper data
                if (analysis.actors._rawLLMResponse.includes('FTC') && analysis.actors._rawLLMResponse.includes('Google')) {
                    console.log('   ✅ Actors response contains expected content');
                } else {
                    console.log('   ⚠️ Actors response may not contain expected content');
                }
            } else {
                console.log('   ❌ Actors raw response not captured');
            }
            
            // Check relationships
            if (analysis.relationships._rawLLMResponse) {
                console.log('   ✅ Relationships raw response captured');
                console.log(`   Relationships response length: ${analysis.relationships._rawLLMResponse.length} characters`);
                
                // Check if it's not the error response
                if (analysis.relationships._rawLLMResponse.includes('Text is not readable')) {
                    console.log('   ❌ Relationships still showing "Text is not readable" error');
                } else {
                    console.log('   ✅ Relationships response looks good');
                }
            } else {
                console.log('   ❌ Relationships raw response not captured');
            }
            
            // Check prompts
            if (analysis.actors._prompt) {
                console.log('   ✅ Actors prompt captured');
                if (analysis.actors._prompt.includes('Document text:')) {
                    console.log('   ✅ Actors prompt contains document text');
                } else {
                    console.log('   ⚠️ Actors prompt may be incomplete');
                }
            }
            
            if (analysis.relationships._prompt) {
                console.log('   ✅ Relationships prompt captured');
                if (analysis.relationships._prompt.includes('Actors to analyze:') && 
                    analysis.relationships._prompt.includes('FTC')) {
                    console.log('   ✅ Relationships prompt contains actors list');
                } else {
                    console.log('   ⚠️ Relationships prompt may be missing actors');
                }
            }
            
        } else {
            console.log('   ❌ Analysis failed - no analysis data returned');
        }
        
    } catch (error) {
        console.log('   ❌ Analysis request failed:', error.message);
    }
    
    // Test with empty text (should be handled gracefully)
    console.log('\n2. Testing with empty text...');
    try {
        const response = await axios.post(`${baseUrl}/api/advanced-analyze`, {
            doc_id: 'test_empty',
            title: 'Empty Test',
            text: '',
            useRealLLM: false
        });
        
        if (response.data.analysis) {
            const analysis = response.data.analysis;
            console.log(`   Actors found: ${analysis.actors.length}`);
            
            if (analysis.relationships.skipped) {
                console.log('   ✅ Relationships correctly skipped for empty text');
                console.log(`   Skip reason: ${analysis.relationships.skip_reason}`);
            } else {
                console.log('   ⚠️ Relationships not skipped for empty text');
            }
        }
        
    } catch (error) {
        console.log('   ❌ Empty text test failed:', error.message);
    }
    
    console.log('\n📊 Raw Responses Fix Summary');
    console.log('============================');
    console.log('✅ Fixed data structure to preserve raw responses');
    console.log('✅ Added proper validation for text and actors');
    console.log('✅ Improved error handling for edge cases');
    console.log('✅ Enhanced prompt construction with proper data');
    
    console.log('\n🎯 Expected Results:');
    console.log('- Raw LLM Responses tab should now show complete prompts');
    console.log('- Raw responses should contain actual LLM output');
    console.log('- No more "Text is not readable" errors');
    console.log('- Proper handling of empty or invalid inputs');
    
    console.log('\n🚀 Test the web interface at http://localhost:3000');
    console.log('Navigate to "Raw LLM Responses" tab after running an analysis!');
}

// Run the test
testRawResponsesFix().catch(console.error);
