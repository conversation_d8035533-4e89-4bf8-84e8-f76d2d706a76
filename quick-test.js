const axios = require('axios');

async function quickTest() {
    try {
        const response = await axios.post('http://localhost:3000/api/advanced-analyze', {
            doc_id: 'test_001',
            title: 'Test',
            text: 'The FTC announced new regulations.',
            useRealLLM: false
        });
        
        console.log('✅ Analysis successful!');
        
        const analysis = response.data.analysis;
        
        // Check raw responses
        console.log('Actors raw response:', !!analysis.actors._rawLLMResponse);
        console.log('Relationships raw response:', !!analysis.relationships._rawLLMResponse);
        console.log('Portrayals raw response:', !!analysis.portrayals._rawLLMResponse);
        
        if (analysis.relationships._rawLLMResponse) {
            console.log('Sample response length:', analysis.relationships._rawLLMResponse.length);
        }
        
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
}

quickTest();
