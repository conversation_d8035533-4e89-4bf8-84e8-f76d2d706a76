const express = require('express');
const path = require('path');
const cors = require('cors');
const { DocumentAnalyzer } = require('./src/document-analyzer');
const { AdvancedDocumentAnalyzer } = require('./src/advanced-document-analyzer');
const { ConfigManager } = require('./src/config-manager');
const { <PERSON><PERSON>, ErrorHandler, PerformanceMonitor } = require('./src/logging');
const { PromptTemplates } = require('./src/prompt-templates');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use((req, res, next) => {
    console.log('📡 Request received:', {
        method: req.method,
        path: req.path,
        headers: req.headers,
        body: req.body
    });
    next();
});
app.use(express.static(path.join(__dirname, 'public')));

// Initialize logging and monitoring
const logger = new Logger({ level: 'info', console: true });
const errorHandler = new ErrorHandler(logger);
const performanceMonitor = new PerformanceMonitor(logger);

// Initialize prompt templates
const promptTemplates = new PromptTemplates();

// Enhanced error response helper
function createErrorResponse(error, context = {}) {
    const errorResponse = {
        error: error.message || 'Unknown error occurred',
        type: error.name || 'Error',
        timestamp: new Date().toISOString(),
        context: context
    };

    // Add specific error details based on error type
    if (error.code) {
        errorResponse.code = error.code;
    }

    if (error.status) {
        errorResponse.status = error.status;
    }

    // Add suggestions based on error type
    if (error.message.includes('API key')) {
        errorResponse.suggestions = [
            'Check your API key is correctly configured',
            'Verify API key has proper permissions',
            'Ensure API key format is correct'
        ];
    } else if (error.message.includes('timeout')) {
        errorResponse.suggestions = [
            'Try with a shorter document',
            'Check your internet connection',
            'Increase timeout settings'
        ];
    } else if (error.message.includes('rate limit')) {
        errorResponse.suggestions = [
            'Wait before making another request',
            'Consider upgrading your API plan',
            'Reduce request frequency'
        ];
    } else {
        errorResponse.suggestions = [
            'Check your input parameters',
            'Try again in a few moments',
            'Contact support if problem persists'
        ];
    }

    return errorResponse;
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// API endpoint for document analysis
app.post('/api/analyze', async (req, res) => {
    const timer = performanceMonitor.startTimer('analyze-api');
    
    try {
        const { doc_id, title, text, optional_known_actors, useRealLLM, provider } = req.body;
        
        // Validate input
        if (!doc_id || !text) {
            return res.status(400).json({ 
                error: 'Missing required fields: doc_id and text are required' 
            });
        }

        if (text.length > 50000) {
            return res.status(400).json({ 
                error: 'Document text too long. Maximum 50,000 characters allowed.' 
            });
        }

        logger.info('Starting document analysis', { doc_id, title, useRealLLM, provider });

        // Create configuration
        const config = new ConfigManager();
        
        // Apply request-specific settings
        if (useRealLLM) {
            config.set('analysis.useRealLLM', true);
        }
        if (provider) {
            config.set('llm.provider', provider);
        }

        // Create document object
        const document = {
            doc_id,
            title: title || null,
            text,
            optional_known_actors: optional_known_actors || []
        };

        // Analyze document
        const analyzer = new DocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.analyze(document);

        const duration = timer.end();
        
        logger.info('Document analysis completed', { 
            doc_id, 
            duration, 
            quality: result.quality.score,
            actors: result.analysis.actors.length,
            relationships: result.analysis.relationships.items.length
        });

        // Add metadata for web UI
        result.metadata = {
            doc_id,
            title: title || null,
            analysis_time: duration,
            timestamp: new Date().toISOString()
        };

        res.json(result);

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();
        
        logger.error('Document analysis failed', { 
            error: error.message, 
            doc_id: req.body.doc_id,
            duration
        });

        errorHandler.handleError(error, { 
            endpoint: '/api/analyze', 
            doc_id: req.body.doc_id 
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/analyze',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

// API endpoint for batch analysis
app.post('/api/batch', async (req, res) => {
    const timer = performanceMonitor.startTimer('batch-api');
    
    try {
        const { documents, useRealLLM, provider } = req.body;
        
        if (!documents || !Array.isArray(documents) || documents.length === 0) {
            return res.status(400).json({ 
                error: 'Invalid input: documents must be a non-empty array' 
            });
        }

        if (documents.length > 50) {
            return res.status(400).json({ 
                error: 'Too many documents. Maximum 50 documents per batch.' 
            });
        }

        logger.info('Starting batch analysis', { 
            documentCount: documents.length, 
            useRealLLM, 
            provider 
        });

        // Create configuration
        const config = new ConfigManager();
        
        // Apply request-specific settings
        if (useRealLLM) {
            config.set('analysis.useRealLLM', true);
        }
        if (provider) {
            config.set('llm.provider', provider);
        }

        const analyzer = new DocumentAnalyzer(config.createAnalyzerOptions());
        const results = [];

        // Process documents in parallel (with reasonable limits)
        const batchSize = 5;
        for (let i = 0; i < documents.length; i += batchSize) {
            const batch = documents.slice(i, i + batchSize);
            const batchPromises = batch.map(async (doc) => {
                try {
                    const document = {
                        doc_id: doc.doc_id,
                        title: doc.title || null,
                        text: doc.text,
                        optional_known_actors: doc.optional_known_actors || []
                    };

                    const result = await analyzer.analyze(document);
                    
                    // Add metadata
                    result.metadata = {
                        doc_id: doc.doc_id,
                        title: doc.title || null,
                        timestamp: new Date().toISOString()
                    };

                    return { success: true, result };
                } catch (error) {
                    logger.error('Document analysis failed in batch', { 
                        doc_id: doc.doc_id, 
                        error: error.message 
                    });
                    
                    return { 
                        success: false, 
                        doc_id: doc.doc_id, 
                        error: error.message 
                    };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }

        const duration = timer.end();
        
        logger.info('Batch analysis completed', { 
            totalDocuments: documents.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            duration
        });

        res.json({
            summary: {
                total: documents.length,
                successful: results.filter(r => r.success).length,
                failed: results.filter(r => !r.success).length,
                duration
            },
            results
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();
        
        logger.error('Batch analysis failed', { 
            error: error.message,
            duration
        });

        errorHandler.handleError(error, { 
            endpoint: '/api/batch',
            documentCount: req.body.documents?.length || 0
        });

        res.status(500).json({ 
            error: 'Internal server error during batch analysis',
            message: error.message 
        });
    }
});

// API endpoints for step-by-step advanced analysis
app.post('/api/advanced/extract-actors', async (req, res) => {
    const timer = performanceMonitor.startTimer('extract-actors-api');

    try {
        const { doc_id, title, text, optional_known_actors, provider, model, apiKey, baseUrl } = req.body;

        // Validate input
        if (!doc_id || !text) {
            return res.status(400).json({
                error: 'Missing required fields: doc_id and text are required'
            });
        }

        logger.info('Starting actor extraction', { doc_id, title, provider });

        // Create configuration for this request
        const config = new ConfigManager();
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);

        // Create document object
        const document = {
            doc_id,
            title: title || null,
            text,
            optional_known_actors: optional_known_actors || []
        };

        // Use AdvancedDocumentAnalyzer for step-by-step analysis
        const analyzer = new AdvancedDocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.extractActors(document);

        const duration = timer.end();

        logger.info('Actor extraction completed', {
            doc_id,
            duration,
            actors: result.actors?.length || 0
        });

        res.json({
            success: true,
            step: 'actors',
            result: result,
            metadata: {
                doc_id,
                title: title || null,
                analysis_time: duration,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();

        logger.error('Actor extraction failed', {
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced/extract-actors',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/advanced/analyze-relationships', async (req, res) => {
    const timer = performanceMonitor.startTimer('analyze-relationships-api');

    try {
        const { doc_id, title, text, actors, provider, model, apiKey, baseUrl } = req.body;

        if (!doc_id || !text || !actors) {
            return res.status(400).json({
                error: 'Missing required fields: doc_id, text, and actors are required'
            });
        }

        logger.info('Starting relationship analysis', { doc_id, actors: actors.length });

        const config = new ConfigManager();
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);

        const document = { doc_id, title: title || null, text };
        const analyzer = new AdvancedDocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.analyzeRelationships(document, actors);

        const duration = timer.end();

        logger.info('Relationship analysis completed', {
            doc_id,
            duration,
            relationships: result.relationships?.items?.length || 0
        });

        res.json({
            success: true,
            step: 'relationships',
            result: result,
            metadata: {
                doc_id,
                analysis_time: duration,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();

        logger.error('Relationship analysis failed', {
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced/analyze-relationships',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/advanced/detect-portrayals', async (req, res) => {
    const timer = performanceMonitor.startTimer('detect-portrayals-api');

    try {
        const { doc_id, title, text, actors, provider, model, apiKey, baseUrl } = req.body;

        if (!doc_id || !text || !actors) {
            return res.status(400).json({
                error: 'Missing required fields: doc_id, text, and actors are required'
            });
        }

        logger.info('Starting portrayal detection', { doc_id, actors: actors.length });

        const config = new ConfigManager();
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);

        const document = { doc_id, title: title || null, text };
        const analyzer = new AdvancedDocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.detectPortrayals(document, actors);

        const duration = timer.end();

        logger.info('Portrayal detection completed', {
            doc_id,
            duration,
            portrayals: result.portrayals?.length || 0
        });

        res.json({
            success: true,
            step: 'portrayals',
            result: result,
            metadata: {
                doc_id,
                analysis_time: duration,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();

        logger.error('Portrayal detection failed', {
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced/detect-portrayals',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/advanced/analyze-issue-scope', async (req, res) => {
    const timer = performanceMonitor.startTimer('analyze-issue-scope-api');

    try {
        const { doc_id, title, text, actors, provider, model, apiKey, baseUrl } = req.body;

        if (!doc_id || !text) {
            return res.status(400).json({
                error: 'Missing required fields: doc_id and text are required'
            });
        }

        logger.info('Starting issue scope analysis', { doc_id });

        const config = new ConfigManager();
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);

        const document = { doc_id, title: title || null, text };
        const analyzer = new AdvancedDocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.analyzeIssueScope(document, actors || []);

        const duration = timer.end();

        logger.info('Issue scope analysis completed', {
            doc_id,
            duration
        });

        res.json({
            success: true,
            step: 'issue_scope',
            result: result,
            metadata: {
                doc_id,
                analysis_time: duration,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();

        logger.error('Issue scope analysis failed', {
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced/analyze-issue-scope',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/advanced/detect-causal-mechanisms', async (req, res) => {
    const timer = performanceMonitor.startTimer('detect-causal-mechanisms-api');

    try {
        const { doc_id, title, text, actors, provider, model, apiKey, baseUrl } = req.body;

        if (!doc_id || !text) {
            return res.status(400).json({
                error: 'Missing required fields: doc_id and text are required'
            });
        }

        logger.info('Starting causal mechanism detection', { doc_id });

        const config = new ConfigManager();
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);

        const document = { doc_id, title: title || null, text };
        const analyzer = new AdvancedDocumentAnalyzer(config.createAnalyzerOptions());
        const result = await analyzer.detectCausalMechanisms(document, actors || []);

        const duration = timer.end();

        logger.info('Causal mechanism detection completed', {
            doc_id,
            duration
        });

        res.json({
            success: true,
            step: 'causal_mechanisms',
            result: result,
            metadata: {
                doc_id,
                analysis_time: duration,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();

        logger.error('Causal mechanism detection failed', {
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced/detect-causal-mechanisms',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

// Advanced document analysis endpoint with multi-step processing
app.post('/api/advanced-analyze', async (req, res) => {
    const timer = performanceMonitor.startTimer('advanced-analyze-api');
    
    try {
        const { doc_id, title, text, optional_known_actors, useRealLLM, provider, model, apiKey, baseUrl } = req.body;
        
        console.log('🔍 Received request body:', req.body);
        
        // Validate input
        if (!doc_id || !text) {
            return res.status(400).json({ 
                error: 'Missing required fields: doc_id and text are required' 
            });
        }

        if (text.length > 50000) {
            return res.status(400).json({
                error: `Document text too long. Maximum 50,000 characters allowed. Current length: ${text.length}`
            });
        }

        // Check for encoding issues (too many question marks or replacement characters)
        const questionMarkRatio = (text.match(/\?/g) || []).length / text.length;
        const replacementCharRatio = (text.match(/�/g) || []).length / text.length;

        if (questionMarkRatio > 0.3 || replacementCharRatio > 0.1) {
            console.warn(`⚠️ Document ${doc_id} may have encoding issues:`, {
                questionMarkRatio: (questionMarkRatio * 100).toFixed(1) + '%',
                replacementCharRatio: (replacementCharRatio * 100).toFixed(1) + '%',
                textLength: text.length
            });
        }

        logger.info('Starting advanced document analysis', { 
            doc_id, 
            title, 
            useRealLLM, 
            provider, 
            model 
        });

        // Configure LLM integration
        const config = new ConfigManager();

        const analyzerConfig = {
            useRealLLM: useRealLLM || false,
            llmConfig: {
                provider: provider || config.config.llm.provider,
                model: model || config.config.llm.model,
                apiKey: apiKey || config.getProviderApiKey(provider || config.config.llm.provider),
                baseUrl: baseUrl || config.config.llm.baseUrl
            }
        };

        // Validate configuration if using real LLM
        if (analyzerConfig.useRealLLM && !analyzerConfig.llmConfig.apiKey) {
            return res.status(400).json({
                error: 'API key is required when useRealLLM is true',
                message: 'Please provide an API key or configure it in the system settings'
            });
        }

  
        // Initialize advanced analyzer
        const analyzer = new AdvancedDocumentAnalyzer(analyzerConfig);
        
        // Prepare document for analysis
        const document = {
            doc_id,
            title: title || null,
            text,
            optional_known_actors: optional_known_actors || []
        };

  
        // Perform advanced analysis
        const result = await analyzer.analyze(document);
        
        const duration = timer.end();
        
        logger.info('Advanced document analysis completed', { 
            doc_id, 
            duration,
            actorsFound: result.analysis.actors.length,
            relationshipsFound: result.analysis.relationships.items.length,
            qualityScore: result.quality.score
        });

        res.json(result);

    } catch (error) {
        const duration = timer.end();
        performanceMonitor.incrementError();
        
        console.error('🚨 Advanced analysis error:', error);
        console.error('🚨 Error stack:', error.stack);
        
        logger.error('Advanced document analysis failed', { 
            error: error.message,
            duration,
            doc_id: req.body.doc_id
        });

        errorHandler.handleError(error, { 
            endpoint: '/api/advanced-analyze',
            doc_id: req.body.doc_id
        });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/advanced-analyze',
            doc_id: req.body.doc_id,
            duration
        });

        res.status(500).json(errorResponse);
    }
});

// API endpoint for configuration management
app.post('/api/config', (req, res) => {
    try {
        const { provider, model, apiKey, baseUrl, useRealLLM } = req.body;
        const config = new ConfigManager();
        
        // Update configuration
        if (provider) config.set('llm.provider', provider);
        if (model) config.set('llm.model', model);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (baseUrl) config.set('llm.baseUrl', baseUrl);
        if (useRealLLM !== undefined) config.set('analysis.useRealLLM', useRealLLM);
        
        // Save configuration
        config.saveConfig();
        
        logger.info('Configuration updated', { provider, model, useRealLLM });
        
        res.json({ 
            success: true, 
            message: 'Configuration saved successfully',
            config: config.getSanitizedConfig()
        });
    } catch (error) {
        logger.error('Failed to save configuration', { error: error.message });
        res.status(500).json({ 
            error: 'Failed to save configuration',
            message: error.message 
        });
    }
});

// API endpoint for testing LLM connection with custom config
app.post('/api/test-llm', async (req, res) => {
    try {
        const { provider, model, apiKey, baseUrl } = req.body;
        
        if (!apiKey) {
            return res.status(400).json({ 
                success: false, 
                message: 'API key is required for testing connection' 
            });
        }
        
        // Create temporary LLM integration for testing
        const { LLMIntegration } = require('./src/llm-integration');
        const llmIntegration = new LLMIntegration({
            provider: provider || 'openai',
            model: model || 'gpt-4o-2024-08-06',
            apiKey: apiKey,
            baseUrl: baseUrl
        });
        
        const result = await llmIntegration.testConnection();
        
        logger.info('LLM connection test completed', { 
            provider: provider || 'openai', 
            model: model || 'gpt-4o-2024-08-06',
            success: result.success 
        });
        
        res.json({
            success: result.success,
            message: result.message,
            provider: llmIntegration.provider,
            model: llmIntegration.model
        });
    } catch (error) {
        logger.error('LLM connection test failed', { error: error.message });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/test-llm',
            provider: req.body.provider,
            model: req.body.model
        });

        res.status(500).json({
            success: false,
            ...errorResponse
        });
    }
});

// API endpoint for getting available models
app.get('/api/models', (req, res) => {
    try {
        // Create a temporary LLM integration without API key to get provider config
        class TempLLMIntegration {
            getProviderConfig() {
                return {
                    openai: {
                        models: [
                            'gpt-4o-2024-08-06',
                            'gpt-4o-mini-2024-07-18',
                            'gpt-4-turbo-2024-04-09',
                            'gpt-4-0125-preview',
                            'gpt-3.5-turbo-0125'
                        ],
                        baseUrl: 'https://api.openai.com/v1',
                        apiKeyEnv: 'OPENAI_API_KEY'
                    },
                    anthropic: {
                        models: [
                            'claude-4-opus-20250201',
                            'claude-4-sonnet-20250201',
                            'claude-4-haiku-20250201',
                            'claude-3-opus-20240229',
                            'claude-3-sonnet-20240229',
                            'claude-3-haiku-20240307'
                        ],
                        baseUrl: 'https://api.anthropic.com',
                        apiKeyEnv: 'ANTHROPIC_API_KEY'
                    },
                    zhipu: {
                        models: [
                            'glm-4.5',
                            'glm-4',
                            'glm-4-air',
                            'glm-4-airx',
                            'glm-3-turbo',
                            'glm-4-vision'
                        ],
                        baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
                        apiKeyEnv: 'ZHIPU_API_KEY'
                    },
                    alibaba: {
                        models: [
                            'qwen-turbo',
                            'qwen-plus',
                            'qwen-max',
                            'qwen-max-longcontext',
                            'qwen-vl-plus'
                        ],
                        baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                        apiKeyEnv: 'DASHSCOPE_API_KEY'
                    },
                    kimi: {
                        models: [
                            'moonshot-v1-8k',
                            'moonshot-v1-32k',
                            'moonshot-v1-128k'
                        ],
                        baseUrl: 'https://api.moonshot.cn/v1',
                        apiKeyEnv: 'MOONSHOT_API_KEY'
                    },
                    deepseek: {
                        models: [
                            'deepseek-chat',
                            'deepseek-coder'
                        ],
                        baseUrl: 'https://api.deepseek.com',
                        apiKeyEnv: 'DEEPSEEK_API_KEY'
                    }
                };
            }
        }
        
        const tempIntegration = new TempLLMIntegration();
        const providerConfig = tempIntegration.getProviderConfig();
        
        res.json({
            providers: Object.keys(providerConfig).map(key => ({
                name: key,
                ...providerConfig[key]
            }))
        });
    } catch (error) {
        logger.error('Failed to get models', { error: error.message });
        res.status(500).json({ 
            error: 'Failed to get models',
            message: error.message 
        });
    }
});

// API endpoint for configuration validation
app.post('/api/validate-config', async (req, res) => {
    try {
        const { provider, model, apiKey, baseUrl } = req.body;
        const errors = [];
        const warnings = [];

        // Validate provider
        if (!provider) {
            errors.push('Provider is required');
        } else if (!['openai', 'anthropic', 'zhipu', 'alibaba', 'kimi', 'deepseek'].includes(provider)) {
            errors.push('Invalid provider');
        }

        // Validate model
        if (!model) {
            errors.push('Model is required');
        }

        // Validate API key
        if (!apiKey || apiKey.trim() === '') {
            errors.push('API key is required');
        } else if (apiKey.startsWith('***')) {
            errors.push('API key appears to be masked');
        } else {
            // Provider-specific API key validation
            switch (provider) {
                case 'zhipu':
                    if (!apiKey.includes('.')) {
                        errors.push('Zhipu API key should contain a dot (.)');
                    }
                    break;
                case 'openai':
                    if (!apiKey.startsWith('sk-')) {
                        errors.push('OpenAI API key should start with "sk-"');
                    }
                    break;
                case 'anthropic':
                    if (!apiKey.startsWith('sk-ant-')) {
                        errors.push('Anthropic API key should start with "sk-ant-"');
                    }
                    break;
            }
        }

        // Validate base URL if provided
        if (baseUrl && baseUrl.trim() !== '') {
            try {
                new URL(baseUrl);
            } catch (e) {
                errors.push('Base URL is not a valid URL');
            }
        }

        // If basic validation passes, try to create LLM integration
        let connectionTest = null;
        if (errors.length === 0) {
            try {
                const { LLMIntegration } = require('./src/llm-integration');
                const llmIntegration = new LLMIntegration({
                    provider,
                    model,
                    apiKey,
                    baseUrl,
                    configOnly: true // Don't initialize client
                });

                // Test if we can create the integration without errors
                const providerInfo = llmIntegration.getProviderInfo();
                connectionTest = {
                    provider: providerInfo.provider,
                    model: providerInfo.model,
                    hasApiKey: !!providerInfo.apiKey
                };

            } catch (integrationError) {
                warnings.push(`LLM integration warning: ${integrationError.message}`);
            }
        }

        const isValid = errors.length === 0;

        res.json({
            valid: isValid,
            errors: errors,
            warnings: warnings,
            details: connectionTest
        });

    } catch (error) {
        logger.error('Failed to validate configuration', { error: error.message });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/validate-config'
        });

        res.status(500).json(errorResponse);
    }
});

// API endpoints for prompt template management
app.get('/api/templates/:templateName', (req, res) => {
    try {
        const { templateName } = req.params;

        if (!promptTemplates.templates[templateName]) {
            return res.status(404).json({
                error: 'Template not found',
                message: `Template '${templateName}' does not exist`
            });
        }

        const template = promptTemplates.getTemplate(templateName);

        res.json({
            templateName,
            template: template,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Failed to get template', { error: error.message, templateName: req.params.templateName });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/templates/:templateName',
            templateName: req.params.templateName
        });

        res.status(500).json(errorResponse);
    }
});

app.put('/api/templates/:templateName', (req, res) => {
    try {
        const { templateName } = req.params;
        const { template } = req.body;

        if (!template || typeof template !== 'string') {
            return res.status(400).json({
                error: 'Invalid template content',
                message: 'Template content must be a non-empty string'
            });
        }

        if (!promptTemplates.templates.hasOwnProperty(templateName)) {
            return res.status(404).json({
                error: 'Template not found',
                message: `Template '${templateName}' does not exist`
            });
        }

        // Update the template
        promptTemplates.updateTemplate(templateName, template);

        logger.info('Template updated', { templateName, length: template.length });

        res.json({
            success: true,
            templateName,
            message: 'Template updated successfully',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Failed to update template', { error: error.message, templateName: req.params.templateName });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/templates/:templateName',
            templateName: req.params.templateName,
            method: 'PUT'
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/templates/:templateName/reset', (req, res) => {
    try {
        const { templateName } = req.params;

        if (!promptTemplates.templates.hasOwnProperty(templateName)) {
            return res.status(404).json({
                error: 'Template not found',
                message: `Template '${templateName}' does not exist`
            });
        }

        // Reset to default template
        const defaultTemplate = promptTemplates.getDefaultTemplate(templateName);
        promptTemplates.updateTemplate(templateName, defaultTemplate);

        logger.info('Template reset to default', { templateName });

        res.json({
            success: true,
            templateName,
            template: defaultTemplate,
            message: 'Template reset to default successfully',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Failed to reset template', { error: error.message, templateName: req.params.templateName });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/templates/:templateName/reset',
            templateName: req.params.templateName
        });

        res.status(500).json(errorResponse);
    }
});

app.post('/api/templates/preview', (req, res) => {
    try {
        const { templateName, template, sampleData } = req.body;

        if (!template || !sampleData) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Template and sampleData are required'
            });
        }

        // Generate preview by replacing variables in template
        const preview = promptTemplates.generatePreview(template, sampleData);

        res.json({
            success: true,
            templateName,
            preview: preview,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Failed to generate template preview', { error: error.message });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/templates/preview'
        });

        res.status(500).json(errorResponse);
    }
});

// API endpoint for configuration
app.get('/api/config', (req, res) => {
    try {
        const config = new ConfigManager();
        const fullConfig = config.getConfig();

        // For frontend display, we need the full API key
        // But we'll mark it as sensitive data
        const responseConfig = {
            ...fullConfig,
            _sensitive: true,
            _note: 'This response contains sensitive data for configuration purposes'
        };

        res.json(responseConfig);
    } catch (error) {
        logger.error('Failed to get configuration', { error: error.message });
        res.status(500).json({
            error: 'Failed to get configuration',
            message: error.message
        });
    }
});

// API endpoint for testing LLM connection
app.get('/api/test-llm', async (req, res) => {
    try {
        const config = new ConfigManager();
        const llmIntegration = config.createLLMIntegration();
        
        const testResult = await llmIntegration.testConnection();
        res.json(testResult);
    } catch (error) {
        logger.error('LLM connection test failed', { error: error.message });

        const errorResponse = createErrorResponse(error, {
            endpoint: '/api/test-llm',
            method: 'GET'
        });

        res.status(500).json(errorResponse);
    }
});

// API endpoint for saving API configuration
app.post('/api/save-api-config', async (req, res) => {
    try {
        console.log('💾 Saving API configuration:', {
            body: req.body,
            headers: req.headers
        });
        
        const { provider, apiKey, model } = req.body;
        
        if (!provider) {
            return res.status(400).json({
                error: 'Provider is required'
            });
        }
        
        const config = new ConfigManager();
        
        // Update config with new values
        if (provider) config.set('llm.provider', provider);
        if (apiKey) config.set('llm.apiKey', apiKey);
        if (model) config.set('llm.model', model);
        
        // Save to file
        const saved = config.saveConfig();
        
        if (saved) {
            logger.info('API configuration saved successfully', { provider, model });
            
            // Test connection if API key was provided
            if (apiKey) {
                try {
                    const llmIntegration = config.createLLMIntegration();
                    const testResult = await llmIntegration.testConnection();
                    
                    return res.json({
                        success: true,
                        message: 'API configuration saved successfully',
                        connectionTest: testResult
                    });
                } catch (connectionError) {
                    logger.warn('API configuration saved but connection test failed', { 
                        error: connectionError.message,
                        provider,
                        model 
                    });
                    
                    return res.json({
                        success: true,
                        message: 'API configuration saved successfully, but connection test failed',
                        connectionTest: {
                            success: false,
                            message: connectionError.message
                        }
                    });
                }
            }
            
            return res.json({
                success: true,
                message: 'API configuration saved successfully'
            });
        } else {
            throw new Error('Failed to save configuration');
        }
    } catch (error) {
        console.error('❌ Failed to save API configuration:', error);
        logger.error('Failed to save API configuration', { error: error.message });
        res.status(500).json({
            error: 'Failed to save API configuration',
            message: error.message
        });
    }
});

// API endpoint for performance metrics
app.get('/api/metrics', (req, res) => {
    try {
        const metrics = performanceMonitor.getMetrics();
        const errorStats = errorHandler.getErrorStatistics();
        
        res.json({
            performance: metrics,
            errors: errorStats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Failed to get metrics', { error: error.message });
        res.status(500).json({ 
            error: 'Failed to get metrics',
            message: error.message 
        });
    }
});

// Serve the main page (now shows advanced interface)
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Serve the advanced interface (redirect to main page)
app.get('/advanced', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Serve the original simple interface
app.get('/simple', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'simple-index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    logger.error('Unhandled error', { 
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method
    });

    errorHandler.handleError(error, { 
        url: req.url,
        method: req.method
    });

    res.status(500).json({ 
        error: 'Internal server error',
        message: error.message 
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ 
        error: 'Not found',
        message: `Route ${req.method} ${req.path} not found` 
    });
});

// Start server
app.listen(PORT, () => {
    logger.info(`Document Analysis Web Server started`, { 
        port: PORT,
        environment: process.env.NODE_ENV || 'development'
    });
    
    console.log(`🚀 Document Analysis Web Server running on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`📈 Metrics: http://localhost:${PORT}/api/metrics`);
    console.log(`🧪 LLM Test: http://localhost:${PORT}/api/test-llm`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    process.exit(0);
});

module.exports = app;