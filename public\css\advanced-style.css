/* Advanced Document Analysis System Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    margin-bottom: 20px;
    padding: 30px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
}

.header-text h1 {
    margin: 0 0 10px 0;
    font-size: 2.2rem;
    font-weight: 700;
}

.header-text p {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
}

.header-status {
    display: flex;
    align-items: center;
}

.connection-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #dc3545;
    animation: pulse 2s infinite;
}

.indicator-dot.connected {
    background: #28a745;
    animation: none;
}

.indicator-text {
    font-size: 0.9rem;
    font-weight: 500;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Navigation */
.main-navigation {
    margin-bottom: 30px;
}

.nav-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 6px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.nav-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    font-weight: 500;
    color: #6c757d;
    white-space: nowrap;
    min-width: 140px;
}

.nav-tab:hover {
    background: #f8f9fa;
    color: #495057;
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-icon {
    font-size: 1.1rem;
}

.nav-text {
    font-weight: 600;
}

/* Main Content */
.main-content {
    min-height: 600px;
}

.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* API Configuration Section */
.api-config {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Configuration Status */
.config-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.status-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.status-value {
    color: #6c757d;
    font-size: 0.9rem;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.status-value.success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.status-value.warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.status-value.error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Request Status */
.request-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.status-row .status-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.status-row .status-value {
    color: #6c757d;
    font-size: 0.9rem;
    padding: 2px 6px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    min-width: 80px;
    text-align: center;
}

.status-row .status-value.active {
    color: #0056b3;
    background-color: #cce7ff;
    border-color: #99d3ff;
}

.status-row .status-value.success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.status-row .status-value.error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Error Details Panel */
.error-details {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid #dc3545;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    z-index: 1000;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.error-details.hidden {
    display: none;
}

.error-header {
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.error-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

.error-content {
    padding: 20px;
}

.error-section {
    margin-bottom: 15px;
}

.error-section h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 0.95rem;
    font-weight: 600;
}

.error-text {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    white-space: pre-wrap;
    word-break: break-word;
}

.error-text.critical {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Error overlay */
.error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
}

.error-overlay.show {
    display: block;
}

/* Debug Panel */
.debug-toggle {
    text-align: center;
    margin: 20px 0;
}

.btn-debug {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.btn-debug:hover {
    background: #5a6268;
}

.debug-panel {
    background: white;
    border-radius: 15px;
    padding: 0;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid #6c757d;
}

.debug-panel.hidden {
    display: none;
}

.debug-header {
    background: #6c757d;
    color: white;
    padding: 15px 20px;
    border-radius: 13px 13px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h2 {
    margin: 0;
    font-size: 1.2rem;
}

.debug-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.debug-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
}

.debug-tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.debug-tab-btn.active {
    color: #495057;
    border-bottom-color: #6c757d;
    background: white;
}

.debug-content {
    padding: 20px;
}

.debug-tab-pane {
    display: none;
}

.debug-tab-pane.active {
    display: block;
}

.debug-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.debug-logs {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 4px solid #6c757d;
}

.log-entry.info {
    background: #d1ecf1;
    border-left-color: #17a2b8;
}

.log-entry.success {
    background: #d4edda;
    border-left-color: #28a745;
}

.log-entry.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.log-entry.error {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.log-timestamp {
    color: #6c757d;
    font-size: 0.8rem;
}

.log-message {
    margin-top: 2px;
}

.system-status {
    display: grid;
    gap: 20px;
}

.status-group h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1rem;
}

.info-content {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.config-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    white-space: pre-wrap;
    overflow-x: auto;
}

.network-requests {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.network-request {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}

.request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.request-method {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
}

.request-method.GET {
    background: #d4edda;
    color: #155724;
}

.request-method.POST {
    background: #cce7ff;
    color: #004085;
}

.request-method.PUT {
    background: #fff3cd;
    color: #856404;
}

.request-method.DELETE {
    background: #f8d7da;
    color: #721c24;
}

.request-url {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #495057;
}

.request-status {
    font-size: 0.85rem;
    padding: 2px 6px;
    border-radius: 3px;
}

.request-status.success {
    background: #d4edda;
    color: #155724;
}

.request-status.error {
    background: #f8d7da;
    color: #721c24;
}

.request-details {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Prompt Templates Section */
.prompt-templates {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.template-selection {
    display: flex;
    gap: 20px;
    align-items: end;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.template-editor {
    margin-bottom: 30px;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.editor-header h3 {
    margin: 0;
    color: #495057;
}

.editor-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.editor-content {
    margin-bottom: 20px;
}

.editor-content label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.editor-content textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 400px;
}

.editor-content textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.template-variables {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.template-variables h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 0.95rem;
}

.variables-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.variable {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.variable:hover {
    background: #0056b3;
}

.template-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.template-preview h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.preview-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
}

/* Raw Responses Section */
.raw-responses-content {
    padding: 20px;
}

.raw-responses-content h3 {
    margin: 0 0 15px 0;
    color: #495057;
}

.response-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.response-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.response-details {
    display: grid;
    gap: 15px;
}

.prompt-section h5,
.response-section h5 {
    margin: 0 0 8px 0;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 600;
}

.prompt-content,
.raw-response {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 400px;
    overflow-y: auto;
    margin: 0;
}

.prompt-content {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.raw-response {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Batch Processing Section */
.batch-processing {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Real-time Request Monitor */
.request-monitor {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.request-monitor h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1rem;
}

.request-list {
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
}

.request-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.request-item.pending {
    border-left: 4px solid #ffc107;
    background: #fff3cd;
}

.request-item.success {
    border-left: 4px solid #28a745;
    background: #d4edda;
}

.request-item.error {
    border-left: 4px solid #dc3545;
    background: #f8d7da;
}

.request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
}

.request-url {
    color: #495057;
    word-break: break-all;
}

.request-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 600;
}

.request-status.pending {
    background: #ffc107;
    color: #212529;
}

.request-status.success {
    background: #28a745;
    color: white;
}

.request-status.error {
    background: #dc3545;
    color: white;
}

.request-details {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.8rem;
}

.request-body,
.request-response {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    margin: 5px 0;
    max-height: 150px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
}

.request-timing {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 5px;
}

/* Batch Options */
.batch-options {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.option-group {
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.option-help {
    display: block;
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 5px;
    margin-left: 25px;
    line-height: 1.4;
}

/* Batch Result Modal */
.batch-result-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #fff;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 1200px;
    height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.tab-btn {
    padding: 15px 25px;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: white;
}

.tab-content {
    flex: 1;
    overflow: auto;
}

.tab-pane {
    display: none;
    padding: 30px;
    height: 100%;
    overflow: auto;
}

.tab-pane.active {
    display: block;
}

/* Modal specific content styles */
.modal-content .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.modal-content .actors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.modal-content .relationships-network {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.raw-response-section {
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.raw-response-section h4 {
    margin: 0;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
    font-size: 1.1rem;
    font-weight: 600;
}

.prompt-section, .response-section {
    padding: 20px;
}

.prompt-section {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.prompt-section h5, .response-section h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.prompt-text, .response-text {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
}

.response-text {
    background-color: #fff;
}

/* Modal open body style */
body.modal-open {
    overflow: hidden;
}

.batch-config {
    margin-bottom: 30px;
}

.folder-info {
    display: flex;
    gap: 15px;
    margin-top: 8px;
    font-size: 0.9rem;
    color: #6c757d;
}

.batch-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.batch-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.batch-progress {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.progress-overview {
    margin-bottom: 20px;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-weight: 600;
    color: #495057;
    min-width: 40px;
}

.current-processing {
    margin-bottom: 20px;
}

.current-files-list {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 150px;
    overflow-y: auto;
}

.processing-log {
    margin-bottom: 20px;
}

.log-content {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}

.batch-results {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.results-summary {
    margin-bottom: 20px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.summary-card.success {
    border-left: 4px solid #28a745;
}

.summary-card.error {
    border-left: 4px solid #dc3545;
}

.summary-card.info {
    border-left: 4px solid #17a2b8;
}

.summary-card .card-title {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.summary-card .card-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
}

.results-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th,
.results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.results-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.results-table tbody tr:hover {
    background: #f8f9fa;
}

.file-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.file-status.success {
    background: #d4edda;
    color: #155724;
}

.file-status.error {
    background: #f8d7da;
    color: #721c24;
}

.file-status.processing {
    background: #fff3cd;
    color: #856404;
}

.file-actions {
    display: flex;
    gap: 5px;
}

.file-actions button {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-actions .btn-view {
    background: #007bff;
    color: white;
}

.file-actions .btn-view:hover {
    background: #0056b3;
}

.file-actions .btn-retry {
    background: #ffc107;
    color: #212529;
}

.file-actions .btn-retry:hover {
    background: #e0a800;
}

.api-config h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    align-items: start;
}

.config-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

/* Special style for the config-group containing buttons */
.config-group.button-group {
    flex-direction: row;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 20px;
    grid-column: 1 / -1; /* Span across all columns */
}

.config-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #4a5568;
    display: block;
}

.config-group input,
.config-group select {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.config-group input:focus,
.config-group select:focus {
    outline: none;
    border-color: #667eea;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Stepwise Analysis */
.stepwise-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.stepwise-actions h3 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.4rem;
}

.stepwise-actions p {
    color: #718096;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.step-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.step-button {
    padding: 8px 16px;
    margin: 5px;
    background-color: #e0e0e0;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.step-button:hover {
    background-color: #d0d0d0;
}

.step-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.step-button.loading {
    background-color: #ffd700;
    animation: pulse 1.5s infinite;
}

.step-button.completed {
    background-color: #8BC34A;
    color: white;
}

.reset-button {
    padding: 8px 16px;
    margin: 5px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-button:hover {
    background-color: #d32f2f;
}

.reset-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Step buttons styling already defined above with step-buttons-container */

.btn-step {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #edf2f7;
    color: #4a5568;
    border-left: 4px solid #667eea;
}

.btn-step:hover {
    background-color: #e2e8f0;
    transform: translateY(-2px);
}

.btn-step:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-step.active {
    background-color: #ebf4ff;
    border-left-color: #4c51bf;
}

.btn-step.completed {
    background-color: #c6f6d5;
    border-left-color: #38a169;
}

/* Buttons */
.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Status Messages */
.status-success {
    background: #c6f6d5;
    color: #22543d;
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
}

.status-error {
    background: #fed7d7;
    color: #742a2a;
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
}

.status-hidden {
    display: none;
}

/* Analysis Section */
.analysis-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.analysis-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #4a5568;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

/* Progress Section */
.progress-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.progress-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.progress-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.step.active {
    background: #e6fffa;
    border-color: #38b2ac;
    color: #234e52;
}

.step.completed {
    background: #c6f6d5;
    border-color: #48bb78;
    color: #22543d;
}

.step-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.step-text {
    font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #e2e8f0;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.5s ease;
}

/* Results Section */
.results-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.results-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.summary-card h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.card-value {
    font-size: 2rem;
    font-weight: bold;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    border-bottom: 2px solid #e2e8f0;
    margin-bottom: 20px;
    overflow-x: auto;
}

.tab-btn {
    padding: 12px 24px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: #4a5568;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn.active {
    color: #667eea;
    border-bottom: 2px solid #667eea;
}

.tab-btn:hover {
    color: #667eea;
}

/* Tab Content */
.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Actors Grid */
.actors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.actor-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.actor-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.actor-card h4 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.actor-category {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 10px;
}

.actor-notes {
    color: #718096;
    font-style: italic;
}

/* Relationships Network */
.relationships-network {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.relationship-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.relationship-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.relationship-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.relationship-actors {
    font-weight: bold;
    color: #4a5568;
}

.relationship-type {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.relationship-evidence {
    background: #edf2f7;
    padding: 10px;
    border-radius: 6px;
    font-style: italic;
    margin-top: 10px;
}

.character-tags {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.character-tag {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.character-hero {
    background: #c6f6d5;
    color: #22543d;
}

.character-villain {
    background: #fed7d7;
    color: #742a2a;
}

.character-victim {
    background: #fef5e7;
    color: #744210;
}

/* Portrayals List */
.portrayals-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.portrayal-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.portrayal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.portrayal-type {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: bold;
    margin-bottom: 10px;
}

.portrayal-hero {
    background: #c6f6d5;
    color: #22543d;
}

.portrayal-victim {
    background: #fef5e7;
    color: #744210;
}

.portrayal-devil {
    background: #fed7d7;
    color: #742a2a;
}

.portrayal-evidence {
    background: #edf2f7;
    padding: 10px;
    border-radius: 6px;
    font-style: italic;
    margin: 10px 0;
}

.portrayal-explanation {
    color: #4a5568;
    font-size: 0.9rem;
}

/* Issue Scope and Causal Mechanisms */
.issue-scope-list,
.causal-mechanisms-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.scope-card,
.causal-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.scope-card:hover,
.causal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.scope-type,
.causal-type {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: bold;
    margin-bottom: 10px;
}

.scope-expansion {
    background: #bee3f8;
    color: #2a4365;
}

.scope-containment {
    background: #fbb6ce;
    color: #702459;
}

.causal-intentional {
    background: #fed7d7;
    color: #742a2a;
}

.causal-inadvertent {
    background: #c6f6d5;
    color: #22543d;
}

/* Metadata */
.metadata-info {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
}

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
}

.metadata-label {
    font-weight: 600;
    color: #4a5568;
}

.metadata-value {
    color: #718096;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-steps {
        grid-template-columns: 1fr;
    }
    
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .actors-grid,
    .relationships-network,
    .portrayals-list,
    .issue-scope-list,
    .causal-mechanisms-list {
        grid-template-columns: 1fr;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}