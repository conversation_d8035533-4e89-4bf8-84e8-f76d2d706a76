/* Enhanced Step Buttons Styling */
.stepwise-actions {
    margin-top: 30px;
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.stepwise-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #667eea, #764ba2, #ff9a9e);
}

.stepwise-actions h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #495057;
    font-weight: 600;
}

.stepwise-actions p {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.step-buttons-container {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
    margin-top: 20px;
    width: 100%;
    justify-content: space-between !important;
}

.step-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 15px 10px 40px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(118, 75, 162, 0.25);
    overflow: hidden;
    letter-spacing: 0.5px;
    text-transform: none;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 18% !important;
    max-width: 200px;
    min-width: 120px;
    text-align: left;
    white-space: nowrap;
    margin-right: 5px;
}

.step-button::before {
    content: attr(class);
    content: attr(id);
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 700;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    box-shadow: 0 0 0 2px rgba(255,255,255,0.1);
}

/* Step button icons and colors */
.step-button::after {
    font-family: 'Arial', sans-serif;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    opacity: 0.7;
}

.step-button.step-1 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.step-button.step-1::after { content: '📋'; }

.step-button.step-2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.step-button.step-2::after { content: '🔗'; }

.step-button.step-3 {
    background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
}
.step-button.step-3::after { content: '🎭'; }

.step-button.step-4 {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}
.step-button.step-4::after { content: '🌐'; }

.step-button.step-5 {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}
.step-button.step-5::after { content: '⚡'; }

.step-button.step-1::before { content: '1'; }
.step-button.step-2::before { content: '2'; }
.step-button.step-3::before { content: '3'; }
.step-button.step-4::before { content: '4'; }
.step-button.step-5::before { content: '5'; }

.step-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(118, 75, 162, 0.4);
}

.step-button .hover-effect {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.step-button:hover .hover-effect {
    opacity: 1;
}

.step-button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(118, 75, 162, 0.3);
}

.step-button.disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
    box-shadow: none;
}

.step-button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Removed duplicate styles */

.reset-button {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    padding: 10px 15px 10px 35px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 0;
    width: auto;
    position: relative;
    text-align: left;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    flex: 0.5;
    min-width: 120px;
    white-space: nowrap;
}

.reset-button::before {
    content: '↺';
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    font-size: 1.1rem;
    color: #6c757d;
}

.reset-button:hover {
    background: #dc3545;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .step-buttons-container {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .step-button {
        flex-basis: 48%;
        min-width: 140px;
    }
    
    .reset-button {
        flex-basis: 48%;
    }
}

@media (max-width: 768px) {
    .stepwise-actions {
        padding: 20px 15px;
    }
    
    .step-button {
        padding: 10px 10px 10px 38px;
        font-size: 0.8rem;
    }
    
    .step-button::before {
        width: 22px;
        height: 22px;
        font-size: 0.85rem;
    }
    
    .step-button::after {
        display: none; /* Hide icons on small screens */
    }
}

@media (max-width: 576px) {
    .step-button {
        flex-basis: 100%;
        margin-bottom: 5px;
    }
    
    .reset-button {
        flex-basis: 100%;
        margin-top: 5px;
    }
    
    .stepwise-actions h3 {
        font-size: 1.1rem;
    }
    
    .stepwise-actions p {
        font-size: 0.85rem;
    }
}
