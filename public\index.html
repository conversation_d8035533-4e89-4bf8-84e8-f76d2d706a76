<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Document Analysis System</title>
    <link rel="stylesheet" href="css/advanced-style.css">
    <link rel="stylesheet" href="css/step-buttons.css">
    <script>
        // Unregister any existing service workers first
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for(let registration of registrations) {
                    registration.unregister();
                    console.log('Service Worker unregistered');
                }
            });
        }
    </script>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="header-text">
                    <h1>Advanced Document Analysis System</h1>
                    <p>Extract actors, relationships, and narrative patterns using multi-step LLM analysis</p>
                </div>
                <div class="header-status">
                    <div class="connection-indicator" id="connectionIndicator">
                        <span class="indicator-dot"></span>
                        <span class="indicator-text">Disconnected</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Navigation -->
        <nav class="main-navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" data-section="config">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">Configuration</span>
                </button>
                <button class="nav-tab" data-section="prompts">
                    <span class="nav-icon">📝</span>
                    <span class="nav-text">Prompt Templates</span>
                </button>
                <button class="nav-tab" data-section="analysis">
                    <span class="nav-icon">🔍</span>
                    <span class="nav-text">Document Analysis</span>
                </button>
                <button class="nav-tab" data-section="batch">
                    <span class="nav-icon">📁</span>
                    <span class="nav-text">Batch Processing</span>
                </button>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- API Configuration Section -->
            <section class="content-section active" id="configSection" data-section="config">
            <h2>API Configuration</h2>
            <div class="config-grid">
                <div class="config-group">
                    <label for="provider">LLM Provider:</label>
                    <select id="provider" name="provider">
                        <option value="zhipu" selected>智谱 AI (Zhipu)</option>
                        <option value="openai">OpenAI</option>
                        <option value="anthropic">Anthropic</option>
                        <option value="alibaba">阿里云 (Alibaba)</option>
                        <option value="kimi">月之暗面 (Kimi)</option>
                        <option value="deepseek">深度求索 (DeepSeek)</option>
                    </select>
                </div>
                
                <div class="config-group">
                    <label for="model">Model:</label>
                    <select id="model" name="model">
                        <!-- Models will be populated based on provider -->
                    </select>
                </div>
                
                <div class="config-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" name="apiKey" placeholder="Enter your API key">
                </div>
                
                <div class="config-group">
                    <label for="baseUrl">Base URL (optional):</label>
                    <input type="url" id="baseUrl" name="baseUrl" placeholder="Default URL will be used">
                </div>
                
                <!-- Real LLM is always used -->
                <div class="config-group">
                    <p class="config-note">This system always uses real LLM APIs</p>
                </div>
                
                <div class="config-group button-group">
                    <button type="button" id="loadConfig" class="btn-secondary">Load Configuration</button>
                    <button type="button" id="validateConfig" class="btn-secondary">Validate Configuration</button>
                    <button type="button" id="testConnection" class="btn-secondary">Test Connection</button>
                    <button type="button" id="saveConfig" class="btn-primary">Save Configuration</button>
                </div>
            </div>

            <!-- Configuration Status -->
            <div id="configStatus" class="config-status">
                <div class="status-item">
                    <span class="status-label">Configuration:</span>
                    <span id="configStatusText" class="status-value">Not loaded</span>
                </div>
                <div class="status-item">
                    <span class="status-label">API Key:</span>
                    <span id="apiKeyStatus" class="status-value">Not set</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Validation:</span>
                    <span id="validationStatus" class="status-value">Not validated</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Last Updated:</span>
                    <span id="configLastUpdated" class="status-value">Never</span>
                </div>
            </div>

            <div id="connectionStatus" class="status-hidden"></div>

            <!-- Error Details Panel -->
            <div id="errorDetails" class="error-details hidden">
                <div class="error-header">
                    <h3>Error Details</h3>
                    <button id="closeErrorDetails" class="close-btn">&times;</button>
                </div>
                <div class="error-content">
                    <div class="error-section">
                        <h4>Error Message:</h4>
                        <div id="errorMessage" class="error-text"></div>
                    </div>
                    <div class="error-section">
                        <h4>Error Type:</h4>
                        <div id="errorType" class="error-text"></div>
                    </div>
                    <div class="error-section">
                        <h4>Timestamp:</h4>
                        <div id="errorTimestamp" class="error-text"></div>
                    </div>
                    <div class="error-section">
                        <h4>Request Details:</h4>
                        <div id="errorRequestDetails" class="error-text"></div>
                    </div>
                    <div class="error-section">
                        <h4>Suggested Actions:</h4>
                        <div id="errorSuggestions" class="error-text"></div>
                    </div>
                </div>
            </div>

            <!-- Debug Panel Toggle -->
            <div class="debug-toggle">
                <button id="toggleDebugPanel" class="btn-debug">🐛 Debug Panel</button>
            </div>
        </section>

        <!-- Debug Panel -->
        <section id="debugPanel" class="debug-panel hidden">
            <div class="debug-header">
                <h2>Debug Information</h2>
                <button id="closeDebugPanel" class="close-btn">&times;</button>
            </div>

            <div class="debug-tabs">
                <button class="debug-tab-btn active" data-tab="logs">Request Logs</button>
                <button class="debug-tab-btn" data-tab="system">System Status</button>
                <button class="debug-tab-btn" data-tab="config">Configuration</button>
                <button class="debug-tab-btn" data-tab="network">Network</button>
            </div>

            <div class="debug-content">
                <!-- Request Logs Tab -->
                <div id="logs-debug-tab" class="debug-tab-pane active">
                    <div class="debug-controls">
                        <button id="clearLogs" class="btn-secondary">Clear Logs</button>
                        <button id="exportLogs" class="btn-secondary">Export Logs</button>
                        <label>
                            <input type="checkbox" id="autoScroll" checked> Auto-scroll
                        </label>
                    </div>
                    <div id="debugLogs" class="debug-logs"></div>
                </div>

                <!-- System Status Tab -->
                <div id="system-debug-tab" class="debug-tab-pane">
                    <div id="systemStatus" class="system-status">
                        <div class="status-group">
                            <h4>Browser Information</h4>
                            <div id="browserInfo" class="info-content"></div>
                        </div>
                        <div class="status-group">
                            <h4>Application State</h4>
                            <div id="appState" class="info-content"></div>
                        </div>
                        <div class="status-group">
                            <h4>Performance Metrics</h4>
                            <div id="performanceMetrics" class="info-content"></div>
                        </div>
                    </div>
                </div>

                <!-- Configuration Tab -->
                <div id="config-debug-tab" class="debug-tab-pane">
                    <div id="configDebug" class="config-debug">
                        <h4>Current Configuration</h4>
                        <pre id="currentConfig" class="config-display"></pre>
                        <h4>Environment Variables</h4>
                        <pre id="envVars" class="config-display"></pre>
                    </div>
                </div>

                <!-- Network Tab -->
                <div id="network-debug-tab" class="debug-tab-pane">
                    <div id="networkDebug" class="network-debug">
                        <h4>Network Requests</h4>
                        <div id="networkRequests" class="network-requests"></div>
                    </div>
                </div>
            </div>
            </section>

            <!-- Prompt Templates Section -->
            <section class="content-section" id="promptsSection" data-section="prompts">
            <h2>Prompt Templates Management</h2>

            <!-- Template Selection -->
            <div class="template-selection">
                <div class="config-group">
                    <label for="templateSelect">Select Template:</label>
                    <select id="templateSelect" name="templateSelect">
                        <option value="entity_extraction">Entity Extraction</option>
                        <option value="relationship_analysis">Relationship Analysis</option>
                        <option value="portrayal_analysis">Portrayal Analysis</option>
                        <option value="issue_scope_analysis">Issue Scope Analysis</option>
                        <option value="causal_mechanism_analysis">Causal Mechanism Analysis</option>
                        <option value="quality_assessment">Quality Assessment</option>
                    </select>
                </div>

                <div class="template-actions">
                    <button id="loadTemplate" class="btn-secondary">Load Template</button>
                    <button id="saveTemplate" class="btn-primary">Save Template</button>
                    <button id="resetTemplate" class="btn-secondary">Reset to Default</button>
                </div>
            </div>

            <!-- Template Editor -->
            <div class="template-editor">
                <div class="editor-header">
                    <h3 id="templateTitle">Template Editor</h3>
                    <div class="editor-info">
                        <span id="templateInfo">Select a template to edit</span>
                    </div>
                </div>

                <div class="editor-content">
                    <label for="templateContent">Template Content:</label>
                    <textarea id="templateContent" rows="20" placeholder="Template content will appear here..."></textarea>
                </div>

                <div class="template-variables">
                    <h4>Available Variables:</h4>
                    <div class="variables-list">
                        <span class="variable">{text}</span>
                        <span class="variable">{actors}</span>
                        <span class="variable">{title}</span>
                        <span class="variable">{doc_id}</span>
                        <span class="variable">{relationships}</span>
                        <span class="variable">{portrayals}</span>
                    </div>
                </div>
            </div>

            <!-- Template Preview -->
            <div class="template-preview">
                <h4>Preview with Sample Data:</h4>
                <button id="previewTemplate" class="btn-secondary">Generate Preview</button>
                <div id="previewContent" class="preview-content"></div>
            </div>
            </section>

            <!-- Batch Processing Section -->
            <section class="content-section" id="batchSection" data-section="batch">
            <h2>Batch File Processing</h2>
            <p>Process multiple text files from a local folder. Only .txt files will be processed.</p>

            <div class="batch-config">
                <div class="config-group">
                    <label for="folderInput">Select Folder:</label>
                    <input type="file" id="folderInput" webkitdirectory directory multiple accept=".txt">
                    <div class="folder-info">
                        <span id="folderPath">No folder selected</span>
                        <span id="fileCount">0 files</span>
                    </div>
                </div>

                <div class="batch-options">
                    <div class="config-group">
                        <label for="batchSize">Batch Size:</label>
                        <select id="batchSize">
                            <option value="1">1 file at a time</option>
                            <option value="3" selected>3 files at a time</option>
                            <option value="5">5 files at a time</option>
                            <option value="10">10 files at a time</option>
                        </select>
                    </div>

                    <div class="config-group">
                        <label for="outputFormat">Output Format:</label>
                        <select id="outputFormat">
                            <option value="json" selected>JSON</option>
                            <option value="csv">CSV</option>
                            <option value="xlsx">Excel</option>
                        </select>
                    </div>
                </div>

                <div class="batch-actions">
                    <button id="startBatchBtn" class="btn-primary" disabled>Start Batch Processing</button>
                    <button id="pauseBatchBtn" class="btn-secondary" disabled>Pause</button>
                    <button id="stopBatchBtn" class="btn-secondary" disabled>Stop</button>
                    <button id="downloadResultsBtn" class="btn-secondary" disabled>Download Results</button>
                </div>
            </div>

            <!-- Batch Progress -->
            <div class="batch-progress" id="batchProgress" style="display: none;">
                <h3>Processing Progress</h3>
                <div class="progress-overview">
                    <div class="progress-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Files:</span>
                            <span id="totalFiles" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Processed:</span>
                            <span id="processedFiles" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Success:</span>
                            <span id="successFiles" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Failed:</span>
                            <span id="failedFiles" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Remaining:</span>
                            <span id="remainingFiles" class="stat-value">0</span>
                        </div>
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div id="batchProgressFill" class="progress-fill"></div>
                        </div>
                        <span id="batchProgressText" class="progress-text">0%</span>
                    </div>
                </div>

                <!-- Current File Processing -->
                <div class="current-processing">
                    <h4>Currently Processing:</h4>
                    <div id="currentFiles" class="current-files-list">
                        <p>No files being processed</p>
                    </div>
                </div>

                <!-- Processing Log -->
                <div class="processing-log">
                    <h4>Processing Log:</h4>
                    <div id="processingLog" class="log-content">
                        <p>Processing log will appear here...</p>
                    </div>
                </div>
            </div>

            <!-- Batch Results -->
            <div class="batch-results" id="batchResults" style="display: none;">
                <h3>Batch Processing Results</h3>
                <div class="results-summary">
                    <div class="summary-cards">
                        <div class="summary-card success">
                            <div class="card-title">Successful</div>
                            <div id="summarySuccess" class="card-value">0</div>
                        </div>
                        <div class="summary-card error">
                            <div class="card-title">Failed</div>
                            <div id="summaryFailed" class="card-value">0</div>
                        </div>
                        <div class="summary-card info">
                            <div class="card-title">Total Time</div>
                            <div id="summaryTime" class="card-value">0s</div>
                        </div>
                        <div class="summary-card info">
                            <div class="card-title">Avg Time/File</div>
                            <div id="summaryAvgTime" class="card-value">0s</div>
                        </div>
                    </div>
                </div>

                <div class="results-table-container">
                    <table id="resultsTable" class="results-table">
                        <thead>
                            <tr>
                                <th>File Name</th>
                                <th>Status</th>
                                <th>Processing Time</th>
                                <th>Actors Found</th>
                                <th>Relationships</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody">
                            <!-- Results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
            </section>

            <!-- Document Analysis Section -->
            <section class="content-section" id="analysisSection" data-section="analysis">
            <h2>Document Analysis</h2>
            <form id="analysisForm">
                <div class="form-group">
                    <label for="docId">Document ID:</label>
                    <input type="text" id="docId" name="docId" value="doc_001" required>
                </div>
                
                <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" id="title" name="title" placeholder="Enter document title">
                </div>
                
                <div class="form-group">
                    <label for="text">Document Text:</label>
                    <textarea id="text" name="text" rows="15" placeholder="Paste your document text here..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="knownActors">Known Actors (comma-separated):</label>
                    <input type="text" id="knownActors" name="knownActors" placeholder="e.g., FTC, Google, Microsoft">
                </div>
                
                <div class="form-actions">
                    <button type="button" id="loadSample" class="btn-secondary">Load Sample Data</button>
                    <button type="submit" id="analyzeBtn" class="btn-primary">Analyze Document</button>
                </div>
                
                <div class="stepwise-actions">
                    <h3>Step-by-Step Analysis</h3>
                    <p>Use these buttons for step-by-step analysis and debugging:</p>
                    <div class="step-buttons-container" style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 10px; justify-content: space-between;">
                        <button id="extractActorsBtn" class="step-button step-1">1. Extract Actors<span class="hover-effect"></span></button>
                        <button id="analyzeRelationshipsBtn" class="step-button step-2">2. Analyze Relationships<span class="hover-effect"></span></button>
                        <button id="detectPortrayalsBtn" class="step-button step-3">3. Detect Portrayals<span class="hover-effect"></span></button>
                        <button id="analyzeIssueScopeBtn" class="step-button step-4">4. Analyze Issue Scope<span class="hover-effect"></span></button>
                        <button id="detectCausalMechanismsBtn" class="step-button step-5">5. Detect Causal Mechanisms<span class="hover-effect"></span></button>
                        <button id="resetAnalysisBtn" class="reset-button">Reset Analysis</button>
                    </div>
                </div>
            </form>
        </section>

        <!-- Analysis Progress -->
        <section id="analysisProgress" class="progress-section hidden">
            <h2>Analysis Progress</h2>

            <!-- Request Status -->
            <div class="request-status">
                <div class="status-row">
                    <span class="status-label">Current Step:</span>
                    <span id="currentStep" class="status-value">Initializing...</span>
                </div>
                <div class="status-row">
                    <span class="status-label">Request Status:</span>
                    <span id="requestStatus" class="status-value">Idle</span>
                </div>
                <div class="status-row">
                    <span class="status-label">Response Time:</span>
                    <span id="responseTime" class="status-value">-</span>
                </div>
                <div class="status-row">
                    <span class="status-label">Progress:</span>
                    <span id="progressPercent" class="status-value">0%</span>
                </div>
            </div>

            <div class="progress-steps">
                <div class="step" id="step-actors">
                    <div class="step-icon">📋</div>
                    <div class="step-text">Extracting Actors</div>
                </div>
                <div class="step" id="step-relationships">
                    <div class="step-icon">🔗</div>
                    <div class="step-text">Analyzing Relationships</div>
                </div>
                <div class="step" id="step-portrayals">
                    <div class="step-icon">🎭</div>
                    <div class="step-text">Detecting Portrayals</div>
                </div>
                <div class="step" id="step-scope">
                    <div class="step-icon">🌐</div>
                    <div class="step-text">Analyzing Issue Scope</div>
                </div>
                <div class="step" id="step-causal">
                    <div class="step-icon">⚡</div>
                    <div class="step-text">Detecting Causal Mechanisms</div>
                </div>
                <div class="step" id="step-decisions">
                    <div class="step-icon">🤖</div>
                    <div class="step-text">Making AI Decisions</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </section>

        <!-- Results Section -->
        <section id="resultsSection" class="results-section hidden">
            <h2>Analysis Results</h2>
            
            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card">
                    <h3>Actors Found</h3>
                    <div class="card-value" id="actorsCount">0</div>
                </div>
                <div class="summary-card">
                    <h3>Relationships</h3>
                    <div class="card-value" id="relationshipsCount">0</div>
                </div>
                <div class="summary-card">
                    <h3>Portrayals</h3>
                    <div class="card-value" id="portrayalsCount">0</div>
                </div>
                <div class="summary-card">
                    <h3>Quality Score</h3>
                    <div class="card-value" id="qualityScore">0%</div>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="actors">Actors</button>
                <button class="tab-btn" data-tab="relationships">Relationships</button>
                <button class="tab-btn" data-tab="portrayals">Portrayals</button>
                <button class="tab-btn" data-tab="issue-scope">Issue Scope</button>
                <button class="tab-btn" data-tab="causal-mechanisms">Causal Mechanisms</button>
                <button class="tab-btn" data-tab="raw-responses">Raw LLM Responses</button>
                <button class="tab-btn" data-tab="metadata">Metadata</button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <div id="actors-tab" class="tab-pane active">
                    <div class="actors-grid" id="actorsGrid">
                        <!-- Actors will be populated here -->
                    </div>
                </div>

                <div id="relationships-tab" class="tab-pane">
                    <div class="relationships-network" id="relationshipsNetwork">
                        <!-- Relationships will be populated here -->
                    </div>
                </div>

                <div id="portrayals-tab" class="tab-pane">
                    <div class="portrayals-list" id="portrayalsList">
                        <!-- Portrayals will be populated here -->
                    </div>
                </div>

                <div id="issue-scope-tab" class="tab-pane">
                    <div class="issue-scope-list" id="issueScopeList">
                        <!-- Issue scope will be populated here -->
                    </div>
                </div>

                <div id="causal-mechanisms-tab" class="tab-pane">
                    <div class="causal-mechanisms-list" id="causalMechanismsList">
                        <!-- Causal mechanisms will be populated here -->
                    </div>
                </div>

                <div id="raw-responses-tab" class="tab-pane">
                    <div class="raw-responses-content">
                        <h3>Raw LLM Responses</h3>
                        <p>This section shows the complete, unprocessed responses from the LLM for each analysis step.</p>

                        <div class="response-section">
                            <h4>Actor Extraction</h4>
                            <div class="response-details">
                                <div class="prompt-section">
                                    <h5>Prompt Used:</h5>
                                    <pre id="actorsPrompt" class="prompt-content">No prompt available</pre>
                                </div>
                                <div class="response-section">
                                    <h5>Raw Response:</h5>
                                    <pre id="actorsRawResponse" class="raw-response">No response available</pre>
                                </div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4>Relationship Analysis</h4>
                            <div class="response-details">
                                <div class="prompt-section">
                                    <h5>Prompt Used:</h5>
                                    <pre id="relationshipsPrompt" class="prompt-content">No prompt available</pre>
                                </div>
                                <div class="response-section">
                                    <h5>Raw Response:</h5>
                                    <pre id="relationshipsRawResponse" class="raw-response">No response available</pre>
                                </div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4>Portrayal Analysis</h4>
                            <div class="response-details">
                                <div class="prompt-section">
                                    <h5>Prompt Used:</h5>
                                    <pre id="portrayalsPrompt" class="prompt-content">No prompt available</pre>
                                </div>
                                <div class="response-section">
                                    <h5>Raw Response:</h5>
                                    <pre id="portrayalsRawResponse" class="raw-response">No response available</pre>
                                </div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4>Issue Scope Analysis</h4>
                            <div class="response-details">
                                <div class="prompt-section">
                                    <h5>Prompt Used:</h5>
                                    <pre id="issueScopePrompt" class="prompt-content">No prompt available</pre>
                                </div>
                                <div class="response-section">
                                    <h5>Raw Response:</h5>
                                    <pre id="issueScopeRawResponse" class="raw-response">No response available</pre>
                                </div>
                            </div>
                        </div>

                        <div class="response-section">
                            <h4>Causal Mechanisms</h4>
                            <div class="response-details">
                                <div class="prompt-section">
                                    <h5>Prompt Used:</h5>
                                    <pre id="causalMechanismsPrompt" class="prompt-content">No prompt available</pre>
                                </div>
                                <div class="response-section">
                                    <h5>Raw Response:</h5>
                                    <pre id="causalMechanismsRawResponse" class="raw-response">No response available</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="metadata-tab" class="tab-pane">
                    <div class="metadata-info" id="metadataInfo">
                        <!-- Metadata will be populated here -->
                    </div>
                </div>
            </div>
            </section>
        </main>
    </div>

    <script src="js/advanced-app.js"></script>
</body>
</html>