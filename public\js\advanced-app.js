class AdvancedDocumentAnalysisUI {
    constructor() {
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeProviderConfig();
        this.loadConfiguration();
        this.loadSampleData();
        
        // Analysis state
        this.currentAnalysis = null;
        this.analysisProgress = 0;
        
        // Initialize step button states
        this.initializeStepButtonsState();

        // Initialize debug functionality
        this.debugLogsArray = [];
        this.networkRequestsArray = [];
        this.initializeDebugPanel();

        // Initialize request monitoring
        this.activeRequests = new Map();
        this.requestCounter = 0;

        // Initialize connection status
        this.updateConnectionStatus(false, 'Not Connected');
        
        // Load default template when page loads
        this.loadDefaultTemplate();
    }

    initializeElements() {
        // API Configuration elements
        this.providerSelect = document.getElementById('provider');
        this.modelSelect = document.getElementById('model');
        this.apiKeyInput = document.getElementById('apiKey');
        this.baseUrlInput = document.getElementById('baseUrl');
        this.useRealLLMCheckbox = document.getElementById('useRealLLM');
        this.loadConfigBtn = document.getElementById('loadConfig');
        this.validateConfigBtn = document.getElementById('validateConfig');
        this.testConnectionBtn = document.getElementById('testConnection');
        this.saveConfigBtn = document.getElementById('saveConfig');
        this.connectionStatus = document.getElementById('connectionStatus');

        // Configuration status elements
        this.configStatusText = document.getElementById('configStatusText');
        this.apiKeyStatus = document.getElementById('apiKeyStatus');
        this.validationStatus = document.getElementById('validationStatus');
        this.configLastUpdated = document.getElementById('configLastUpdated');

        // Request status elements
        this.currentStep = document.getElementById('currentStep');
        this.requestStatus = document.getElementById('requestStatus');
        this.responseTime = document.getElementById('responseTime');
        this.progressPercent = document.getElementById('progressPercent');

        // Error details elements
        this.errorDetails = document.getElementById('errorDetails');
        this.closeErrorDetailsBtn = document.getElementById('closeErrorDetails');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorType = document.getElementById('errorType');
        this.errorTimestamp = document.getElementById('errorTimestamp');
        this.errorRequestDetails = document.getElementById('errorRequestDetails');
        this.errorSuggestions = document.getElementById('errorSuggestions');

        // Debug panel elements
        this.toggleDebugPanelBtn = document.getElementById('toggleDebugPanel');
        this.debugPanel = document.getElementById('debugPanel');
        this.closeDebugPanelBtn = document.getElementById('closeDebugPanel');
        this.debugTabButtons = document.querySelectorAll('.debug-tab-btn');
        this.debugTabPanes = document.querySelectorAll('.debug-tab-pane');
        this.debugLogs = document.getElementById('debugLogs');
        this.clearLogsBtn = document.getElementById('clearLogs');
        this.exportLogsBtn = document.getElementById('exportLogs');
        this.autoScrollCheckbox = document.getElementById('autoScroll');
        this.systemStatus = document.getElementById('systemStatus');
        this.currentConfig = document.getElementById('currentConfig');
        this.networkRequests = document.getElementById('networkRequests');

        // Prompt template elements
        this.templateSelect = document.getElementById('templateSelect');
        this.loadTemplateBtn = document.getElementById('loadTemplate');
        this.saveTemplateBtn = document.getElementById('saveTemplate');
        this.resetTemplateBtn = document.getElementById('resetTemplate');
        this.templateTitle = document.getElementById('templateTitle');
        this.templateInfo = document.getElementById('templateInfo');
        this.templateContent = document.getElementById('templateContent');
        this.previewTemplateBtn = document.getElementById('previewTemplate');
        this.previewContent = document.getElementById('previewContent');

        // Batch processing elements
        this.folderInput = document.getElementById('folderInput');
        this.folderPath = document.getElementById('folderPath');
        this.fileCount = document.getElementById('fileCount');
        this.batchSize = document.getElementById('batchSize');
        this.outputFormat = document.getElementById('outputFormat');
        this.startBatchBtn = document.getElementById('startBatchBtn');
        this.pauseBatchBtn = document.getElementById('pauseBatchBtn');
        this.stopBatchBtn = document.getElementById('stopBatchBtn');
        this.downloadResultsBtn = document.getElementById('downloadResultsBtn');
        this.batchProgress = document.getElementById('batchProgress');
        this.batchResults = document.getElementById('batchResults');

        // Batch progress elements
        this.totalFiles = document.getElementById('totalFiles');
        this.processedFiles = document.getElementById('processedFiles');
        this.successFiles = document.getElementById('successFiles');
        this.failedFiles = document.getElementById('failedFiles');
        this.remainingFiles = document.getElementById('remainingFiles');
        this.batchProgressFill = document.getElementById('batchProgressFill');
        this.batchProgressText = document.getElementById('batchProgressText');
        this.currentFiles = document.getElementById('currentFiles');
        this.processingLog = document.getElementById('processingLog');
        this.requestList = document.getElementById('requestList');
        this.splitLongDocuments = document.getElementById('splitLongDocuments');
        this.resultsTableBody = document.getElementById('resultsTableBody');

        // Navigation elements
        this.navTabs = document.querySelectorAll('.nav-tab');
        this.contentSections = document.querySelectorAll('.content-section');
        this.connectionIndicator = document.getElementById('connectionIndicator');

        // Analysis form elements
        this.analysisForm = document.getElementById('analysisForm');
        this.docIdInput = document.getElementById('docId');
        this.titleInput = document.getElementById('title');
        this.textInput = document.getElementById('text');
        this.knownActorsInput = document.getElementById('knownActors');
        this.loadSampleBtn = document.getElementById('loadSample');
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.resetAnalysisBtn = document.getElementById('resetAnalysisBtn');
        
        // Step-by-step analysis buttons
        this.extractActorsBtn = document.getElementById('extractActorsBtn');
        this.analyzeRelationshipsBtn = document.getElementById('analyzeRelationshipsBtn');
        this.detectPortrayalsBtn = document.getElementById('detectPortrayalsBtn');
        this.analyzeIssueScopeBtn = document.getElementById('analyzeIssueScopeBtn');
        this.detectCausalMechanismsBtn = document.getElementById('detectCausalMechanismsBtn');

        // Progress elements
        this.progressSection = document.getElementById('analysisProgress');
        this.progressFill = document.getElementById('progressFill');
        this.progressSteps = [
            'step-actors',
            'step-relationships',
            'step-portrayals',
            'step-scope',
            'step-causal',
            'step-decisions'
        ];

        // Results elements
        this.resultsSection = document.getElementById('resultsSection');
        this.tabButtons = document.querySelectorAll('.tab-btn');
        this.tabPanes = document.querySelectorAll('.tab-pane');
        
        // Summary elements
        this.actorsCount = document.getElementById('actorsCount');
        this.relationshipsCount = document.getElementById('relationshipsCount');
        this.portrayalsCount = document.getElementById('portrayalsCount');
        this.qualityScore = document.getElementById('qualityScore');
    }

    initializeEventListeners() {
        // API Configuration listeners
        this.providerSelect.addEventListener('change', () => this.updateModelOptions());
        this.providerSelect.addEventListener('change', () => this.updateBaseUrl());
        this.loadConfigBtn.addEventListener('click', () => this.loadConfigurationFromServer());
        this.validateConfigBtn.addEventListener('click', () => this.validateConfiguration());
        this.testConnectionBtn.addEventListener('click', () => this.testConnection());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfiguration());
        this.useRealLLMCheckbox?.addEventListener('change', () => this.updateRealLLMState());
        
        // Analysis form listeners
        this.loadSampleBtn.addEventListener('click', () => this.loadSampleData());
        this.analysisForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.analyzeDocument();
        });
        
        // Step-by-step analysis listeners
        this.extractActorsBtn.addEventListener('click', () => this.extractActors());
        this.analyzeRelationshipsBtn.addEventListener('click', () => this.analyzeRelationships());
        this.detectPortrayalsBtn.addEventListener('click', () => this.detectPortrayals());
        this.analyzeIssueScopeBtn.addEventListener('click', () => this.analyzeIssueScope());
        this.detectCausalMechanismsBtn.addEventListener('click', () => this.detectCausalMechanisms());
        
        // Reset analysis
        this.resetAnalysisBtn.addEventListener('click', () => this.resetAnalysis());
        
        // Tab navigation
        this.tabButtons.forEach(button => {
            button.addEventListener('click', () => this.switchTab(button.dataset.tab));
        });

        // Error details panel
        this.closeErrorDetailsBtn.addEventListener('click', () => this.hideErrorDetails());

        // Close error panel when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('error-overlay')) {
                this.hideErrorDetails();
            }
        });

        // Debug panel listeners
        this.toggleDebugPanelBtn.addEventListener('click', () => this.toggleDebugPanel());
        this.closeDebugPanelBtn.addEventListener('click', () => this.hideDebugPanel());
        this.clearLogsBtn.addEventListener('click', () => this.clearDebugLogs());
        this.exportLogsBtn.addEventListener('click', () => this.exportDebugLogs());

        // Debug tab navigation
        this.debugTabButtons.forEach(button => {
            button.addEventListener('click', () => this.switchDebugTab(button.dataset.tab));
        });

        // Prompt template listeners
        this.templateSelect.addEventListener('change', () => this.onTemplateSelectionChange());
        this.loadTemplateBtn.addEventListener('click', () => this.loadSelectedTemplate());
        this.saveTemplateBtn.addEventListener('click', () => this.saveCurrentTemplate());
        this.resetTemplateBtn.addEventListener('click', () => this.resetCurrentTemplate());
        this.previewTemplateBtn.addEventListener('click', () => this.previewCurrentTemplate());

        // Variable insertion for template editor
        document.querySelectorAll('.variable').forEach(variable => {
            variable.addEventListener('click', () => this.insertVariable(variable.textContent));
        });

        // Batch processing listeners
        this.folderInput.addEventListener('change', () => this.onFolderSelected());
        this.startBatchBtn.addEventListener('click', () => this.startBatchProcessing());
        this.pauseBatchBtn.addEventListener('click', () => this.pauseBatchProcessing());
        this.stopBatchBtn.addEventListener('click', () => this.stopBatchProcessing());
        this.downloadResultsBtn.addEventListener('click', () => this.downloadBatchResults());

        // Navigation listeners
        this.navTabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchSection(tab.dataset.section));
        });

        // Real LLM is always enabled
    }

    initializeProviderConfig() {
        this.providerConfig = {
            zhipu: { // Setting zhipu as the first provider so it appears first in the list
                models: [
                    'glm-4.5',
                    'glm-4-plus',
                    'glm-4-air',
                    'glm-4-airx',
                    'glm-4-flash',
                    'glm-4-flashx',
                    'glm-4-long',
                    'glm-4v',
                    'glm-3-turbo',
                    'glm-3-turbo-plus'
                ],
                baseUrl: 'https://open.bigmodel.cn/api/paas/v4'
            },
            openai: {
                models: [
                    'gpt-4o-2024-08-06',
                    'gpt-4o-mini-2024-07-18',
                    'gpt-4-turbo-2024-04-09',
                    'gpt-4-0125-preview',
                    'gpt-3.5-turbo-0125'
                ],
                baseUrl: 'https://api.openai.com/v1'
            },
            anthropic: {
                models: [
                    'claude-4-opus-20250201',
                    'claude-4-sonnet-20250201',
                    'claude-4-haiku-20250201',
                    'claude-3-opus-20240229',
                    'claude-3-sonnet-20240229',
                    'claude-3-haiku-20240307'
                ],
                baseUrl: 'https://api.anthropic.com'
            },
            zhipu: {
                models: [
                    'glm-4.5',
                    'glm-4.5-air',
                    'glm-4.5-x',
                    'glm-4.5-airx',
                    'glm-4.5-flash',
                    'glm-4-plus',
                    'glm-4-air-250414',
                    'glm-4-airx',
                    'glm-4-flashx',
                    'glm-4-flashx-250414',
                    'glm-z1-air',
                    'glm-z1-airx',
                    'glm-z1-flash',
                    'glm-z1-flashx'
                ],
                baseUrl: 'https://open.bigmodel.cn/api/paas/v4'
            },
            alibaba: {
                models: [
                    'qwen-turbo',
                    'qwen-plus',
                    'qwen-max',
                    'qwen-max-longcontext',
                    'qwen-vl-plus'
                ],
                baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
            },
            kimi: {
                models: [
                    'moonshot-v1-8k',
                    'moonshot-v1-32k',
                    'moonshot-v1-128k'
                ],
                baseUrl: 'https://api.moonshot.cn/v1'
            },
            deepseek: {
                models: [
                    'deepseek-chat',
                    'deepseek-coder'
                ],
                baseUrl: 'https://api.deepseek.com'
            }
        };
        
        this.updateModelOptions();
        this.updateBaseUrl();
    }

    updateModelOptions() {
        const provider = this.providerSelect.value;
        const models = this.providerConfig[provider].models;
        
        this.modelSelect.innerHTML = '';
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            this.modelSelect.appendChild(option);
        });
    }

    updateBaseUrl() {
        const provider = this.providerSelect.value;
        this.baseUrlInput.placeholder = `Default: ${this.providerConfig[provider].baseUrl}`;
    }

    updateRealLLMState() {
        // Always use real LLM mode, set all fields to enabled/full opacity
        const apiFormFields = [this.providerSelect, this.modelSelect, this.apiKeyInput, this.baseUrlInput];
        apiFormFields.forEach(field => {
            field.style.opacity = '1';
        });
        
        this.testConnectionBtn.disabled = false;
        this.testConnectionBtn.style.opacity = '1';
    }

    loadConfiguration() {
        // First try to load from server
        fetch('/api/config')
            .then(response => response.json())
            .then(serverConfig => {
                // Apply server configuration
                if (serverConfig && serverConfig.llm) {
                    const config = {
                        provider: serverConfig.llm.provider || 'zhipu', // Default to Zhipu if not specified
                        model: serverConfig.llm.model,
                        apiKey: serverConfig.llm.apiKey,
                        useRealLLM: true  // Always use real LLM
                    };
                    this.applyConfiguration(config);
                    this.updateConfigurationStatus('Loaded from server', config.apiKey);
                } else {
                    // If no server config, try local storage
                    this.loadLocalConfiguration();
                }
            })
            .catch(error => {
                console.error('Error loading server configuration:', error);
                // Fallback to local storage if server request fails
                this.loadLocalConfiguration();
                this.updateConfigurationStatus('Failed to load from server', null, 'error');
            });
    }

    loadLocalConfiguration() {
        try {
            const savedConfig = localStorage.getItem('advanced_document_analyzer_config');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                this.applyConfiguration(config);
            } else {
                // If no saved configuration anywhere, default to Zhipu
                this.applyConfiguration({
                    provider: 'zhipu',
                    model: 'glm-4.5',
                    apiKey: '',
                    useRealLLM: true  // Always use real LLM
                });
            }
        } catch (error) {
            console.error('Error loading local configuration:', error);
            // Default to Zhipu if everything fails
            this.applyConfiguration({
                provider: 'zhipu',
                model: 'glm-4.5',
                apiKey: '',
                useRealLLM: true  // Always use real LLM
            });
        }
    }

    applyConfiguration(config) {
        if (config.provider) this.providerSelect.value = config.provider;
        if (config.model) this.modelSelect.value = config.model;
        // Only set API key if it's not masked (doesn't start with ***)
        if (config.apiKey && !config.apiKey.startsWith('***')) {
            this.apiKeyInput.value = config.apiKey;
        }
        if (config.baseUrl) this.baseUrlInput.value = config.baseUrl;
        
        // Always use real LLM
        this.updateRealLLMState();
        
        this.updateModelOptions();
        this.updateBaseUrl();
    }

    saveConfiguration() {
        // Get the form values
        const provider = this.providerSelect.value;
        const model = this.modelSelect.value;
        const apiKey = this.apiKeyInput.value;
        const baseUrl = this.baseUrlInput.value;
        const useRealLLM = true;  // Always use real LLM
        
        // Save to local storage for immediate UI use
        const localConfig = {
            provider: provider,
            model: model,
            apiKey: apiKey.startsWith('***') ? '' : apiKey, // Don't store masked keys
            baseUrl: baseUrl,
            useRealLLM: true  // Always use real LLM
        };
        
        localStorage.setItem('advanced_document_analyzer_config', JSON.stringify(localConfig));
        this.showStatus('Configuration saved locally', 'success');
        this.updateConfigurationStatus('Saved locally', apiKey);
        
        // Don't send masked keys to the server
        const serverConfig = {
            provider: provider,
            model: model,
            apiKey: apiKey.startsWith('***') ? '' : apiKey,
            useRealLLM: true  // Always use real LLM
        };
        
        // If there's no API key to save to server, just use local storage
        if (!serverConfig.apiKey) {
            return;
        }
        
        this.showStatus('Saving to server...', 'info');
        
        // Use XMLHttpRequest instead of fetch for better error handling
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/save-api-config', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const result = JSON.parse(xhr.responseText);
                        if (result.success) {
                            this.showStatus('Configuration saved to server', 'success');
                            this.updateConfigurationStatus('Saved to server', apiKey);

                            // If connection test was performed, show result
                            if (result.connectionTest) {
                                const testSuccess = result.connectionTest.success;
                                const testMsg = result.connectionTest.message ||
                                            (testSuccess ? 'Connection successful' : 'Connection failed');

                                this.showStatus(testMsg, testSuccess ? 'success' : 'error');
                            }
                        } else {
                            this.showStatus(`Error: ${result.error || 'Unknown error'}`, 'error');
                            this.updateConfigurationStatus('Save failed', apiKey, 'error');
                        }
                    } catch (parseError) {
                        console.error('Error parsing server response:', parseError);
                        this.showStatus('Error processing server response', 'error');
                    }
                } else {
                    console.error('Server error:', xhr.status, xhr.statusText);
                    this.showStatus(`Server error (${xhr.status})`, 'error');
                }
            }
        };
        
        xhr.onerror = () => {
            console.error('Network error when saving configuration');
            this.showStatus('Network error when saving to server', 'error');
        };
        
        try {
            xhr.send(JSON.stringify(serverConfig));
        } catch (sendError) {
            console.error('Error sending request:', sendError);
            this.showStatus('Error sending request to server', 'error');
        }
    }

    async testConnection() {
        const config = {
            provider: this.providerSelect.value,
            model: this.modelSelect.value,
            apiKey: this.apiKeyInput.value,
            baseUrl: this.baseUrlInput.value || this.providerConfig[this.providerSelect.value].baseUrl
        };

        // Check if API key is provided
        if (!config.apiKey || config.apiKey.trim() === '') {
            this.showStatus('Please enter an API key before testing connection', 'error');
            return;
        }

        this.testConnectionBtn.innerHTML = '<span class="loading"></span> Testing...';
        this.testConnectionBtn.disabled = true;

        const startTime = Date.now();
        const requestId = this.requestCounter++;
        this.updateRequestStatus('Testing connection', 'Sending test request', null, 0);

        try {
            // Add request to monitor for debugging
            this.addRequestToMonitor(requestId, 'POST', '/api/test-llm', config, 'Connection Test');

            const response = await fetch('/api/test-llm', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });

            const requestTime = Date.now() - startTime;
            this.updateRequestStatus('Processing response', 'Received response', requestTime, 50);

            const result = await response.json();

            const finalTime = Date.now() - startTime;

            if (result.success) {
                this.updateRequestInMonitor(requestId, 'success', result, finalTime);
                this.updateRequestStatus('Connection test', 'Success', finalTime, 100);
                this.showStatus('API connection successful!', 'success');
                this.updateConnectionStatus(true, 'API Connected');
            } else {
                this.updateRequestInMonitor(requestId, 'error', result, finalTime);
                this.updateRequestStatus('Connection test', 'Failed', finalTime, 0);
                this.showStatus('API connection failed: ' + result.message, 'error');
                this.updateConnectionStatus(false, 'Connection Failed');
            }
        } catch (error) {
            const errorTime = Date.now() - startTime;
            this.updateRequestInMonitor(requestId, 'error', { error: error.message }, errorTime);
            this.handleError(error, {
                url: '/api/test-llm',
                method: 'POST',
                data: config,
                timing: errorTime
            });
        } finally {
            this.testConnectionBtn.innerHTML = 'Test Connection';
            this.testConnectionBtn.disabled = false;
        }
    }

    showStatus(message, type) {
        this.connectionStatus.className = `status-${type}`;
        this.connectionStatus.textContent = message;
        this.connectionStatus.classList.remove('status-hidden');
        
        setTimeout(() => {
            this.connectionStatus.classList.add('status-hidden');
        }, 5000);
    }

    loadSampleData() {
        this.docIdInput.value = 'sample_001';
        this.titleInput.value = 'Technology Policy Debate: FTC vs Big Tech';
        this.textInput.value = `The Federal Trade Commission (FTC) announced new regulations targeting Big Tech companies today, marking a significant escalation in government oversight of the technology sector. The proposed rules would require major platforms like Google and Microsoft to obtain explicit user consent before collecting personal data for advertising purposes.

FTC Chair Lina Khan stated that "these protections are essential for safeguarding consumer privacy in the digital age." The Commission voted 3-2 along party lines to advance the proposal, which now faces a 60-day public comment period.

Google quickly criticized the new regulations, with a spokesperson calling the rules "overly restrictive and harmful to innovation." The tech giant argued that such constraints would "undermine the free internet model that has driven digital progress for decades."

In contrast, Microsoft expressed support for the FTC's approach, arguing that "clear privacy guidelines are necessary for maintaining consumer trust and enabling fair competition." The company suggested that well-crafted regulations could "actually benefit the ecosystem by creating a level playing field."

Consumer advocacy groups praised the move, with the Digital Privacy Alliance calling it "a landmark step toward giving users control over their personal information." However, industry analysts warned about potential negative impacts on innovation, suggesting that compliance costs could disproportionately affect smaller startups and limit new entrants to the market.

The debate highlights the ongoing tension between privacy protection and technological innovation, with stakeholders deeply divided on the appropriate role of government regulation in the digital economy.`;

        this.knownActorsInput.value = 'FTC, Google, Microsoft';
    }

    async analyzeDocument() {
        // Get analysis data from form
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        console.log('Sending analysis data:', analysisData);
        await this.startAnalysis(analysisData);
    }
    
    getAnalysisData() {
        // Validate form elements exist
        if (!this.analysisForm || !this.docIdInput || !this.titleInput || !this.textInput) {
            console.error('Form elements not found:', {
                analysisForm: !!this.analysisForm,
                docIdInput: !!this.docIdInput,
                titleInput: !!this.titleInput,
                textInput: !!this.textInput
            });
            this.showStatus('Form elements not properly initialized', 'error');
            return null;
        }
        
        const formData = new FormData(this.analysisForm);
        const docId = formData.get('docId');
        const title = formData.get('title');
        const text = formData.get('text');
        const knownActors = formData.get('knownActors');
        
        if (!docId || !text) {
            console.error('Missing required fields:', { docId: !!docId, text: !!text });
            this.showStatus('Document ID and text are required', 'error');
            return null;
        }
        
        return {
            doc_id: docId,
            title: title,
            text: text,
            optional_known_actors: knownActors ? knownActors.split(',').map(s => s.trim()).filter(s => s) : [],
            useRealLLM: true,  // Always use real LLM
            provider: this.providerSelect.value,
            model: this.modelSelect.value,
            apiKey: this.apiKeyInput.value,
            baseUrl: this.baseUrlInput.value
        };
    }

    async startAnalysis(data) {
        this.logDebug('Starting document analysis', 'info', { doc_id: data.doc_id, provider: data.provider });

        // Show progress section
        this.progressSection.classList.remove('hidden');
        this.resultsSection.classList.add('hidden');

        // Reset progress and initialize step buttons
        this.resetProgress();
        this.resetRequestStatus();
        this.initializeStepButtonsState();

        // Update main analyze button state
        this.analyzeBtn.disabled = true;
        this.analyzeBtn.innerHTML = '<span class="loading"></span> Analyzing...';

        // Track request timing
        const startTime = Date.now();

        try {
            this.updateRequestStatus('Preparing request', 'Sending to server', null, 10);
            this.logDebug('Preparing analysis request', 'info', { endpoint: '/api/advanced-analyze' });

            console.log('Sending request to /api/advanced-analyze with data:', data);
            const response = await fetch('/api/advanced-analyze', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            const requestTime = Date.now() - startTime;
            this.updateRequestStatus('Processing response', 'Received response', requestTime, 30);
            this.logNetworkRequest('POST', '/api/advanced-analyze', response.status, requestTime);

            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('Error response:', errorText);
                this.updateRequestStatus('Request failed', `HTTP error: ${response.status}`, requestTime, 0);
                this.logDebug('Analysis request failed', 'error', { status: response.status, error: errorText });
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Analysis result:', result);
            this.currentAnalysis = result;
            this.logDebug('Analysis response received', 'success', {
                actors: result.analysis?.actors?.length || 0,
                relationships: result.analysis?.relationships?.items?.length || 0,
                quality: result.quality?.score || 0
            });

            const totalTime = Date.now() - startTime;
            this.updateRequestStatus('Parsing results', 'Processing data', totalTime, 60);

            // Simulate progress steps
            await this.simulateProgress();

            // Show results
            this.displayResults(result);

            const finalTime = Date.now() - startTime;
            this.updateRequestStatus('Analysis complete', 'Success', finalTime, 100);
            this.logDebug('Document analysis completed successfully', 'success', {
                totalTime: finalTime,
                doc_id: data.doc_id
            });

        } catch (error) {
            console.error('Analysis error:', error);
            const errorTime = Date.now() - startTime;
            this.logDebug('Analysis failed', 'error', {
                error: error.message,
                timing: errorTime,
                doc_id: data.doc_id
            });
            this.handleError(error, {
                url: '/api/advanced-analyze',
                method: 'POST',
                data: data,
                timing: errorTime
            });
        } finally {
            this.analyzeBtn.disabled = false;
            this.analyzeBtn.innerHTML = 'Analyze Document';
        }
    }
    
    // Step 1: Extract Actors
    async extractActors() {
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        // Disable button and show loading state
        this.extractActorsBtn.disabled = true;
        this.extractActorsBtn.innerHTML = '<span class="loading"></span> Extracting...';
        this.extractActorsBtn.classList.add('loading');
        
        // Update progress indicator
        document.getElementById('step-actors').classList.add('active');
        document.getElementById('step-actors').classList.remove('completed');
        
        // Show status message
        this.showStatus('Extracting actors from document...', 'info');
        
        try {
            console.log('Extracting actors with data:', analysisData);
            const response = await fetch('/api/advanced/extract-actors', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, ${errorText}`);
            }

            const result = await response.json();
            console.log('Actors extraction result:', result);
            
            // Store in current analysis object if it doesn't exist
            if (!this.currentAnalysis) {
                this.currentAnalysis = { analysis: {}, metadata: {} };
            }
            
            // Update the actors in the analysis
            this.currentAnalysis.analysis.actors = result.actors;
            
            // Update UI
            document.getElementById('step-actors').classList.remove('active');
            document.getElementById('step-actors').classList.add('completed');
            this.populateActorsTab(result.actors);
            this.actorsCount.textContent = result.actors.length;
            
            // Show results section and switch to actors tab
            this.resultsSection.classList.remove('hidden');
            this.switchTab('actors');
            
            // Update button state
            this.extractActorsBtn.classList.remove('loading');
            this.extractActorsBtn.classList.add('completed');
            this.extractActorsBtn.innerHTML = '✓ 1. Extract Actors';
            
            this.showStatus(`Successfully extracted ${result.actors.length} actors from document`, 'success');
            
            // Enable the next step button
            this.analyzeRelationshipsBtn.disabled = false;
        } catch (error) {
            console.error('Actor extraction error:', error);
            this.showStatus('Actor extraction failed: ' + error.message, 'error');
            
            // Remove active state from progress indicator
            document.getElementById('step-actors').classList.remove('active');
            
            // Reset analysis state for this step
            if (this.currentAnalysis && this.currentAnalysis.analysis) {
                this.currentAnalysis.analysis.actors = [];
            }
        } finally {
            // Reset button state
            this.extractActorsBtn.disabled = false;
            this.extractActorsBtn.classList.remove('loading');
            this.extractActorsBtn.innerHTML = '1. Extract Actors';
        }
    }
    
    // Step 2: Analyze Relationships
    async analyzeRelationships() {
        if (!this.currentAnalysis || !this.currentAnalysis.analysis.actors) {
            this.showStatus('Please extract actors first', 'error');
            return;
        }
        
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        // Add actors to the request
        analysisData.actors = this.currentAnalysis.analysis.actors;
        
        // Disable button and show loading state
        this.analyzeRelationshipsBtn.disabled = true;
        this.analyzeRelationshipsBtn.innerHTML = '<span class="loading"></span> Analyzing...';
        this.analyzeRelationshipsBtn.classList.add('loading');
        
        // Update progress indicator
        document.getElementById('step-relationships').classList.add('active');
        document.getElementById('step-relationships').classList.remove('completed');
        
        // Show status message
        this.showStatus('Analyzing relationships between actors...', 'info');
        
        try {
            console.log('Analyzing relationships with data:', analysisData);
            const response = await fetch('/api/advanced/analyze-relationships', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, ${errorText}`);
            }

            const result = await response.json();
            console.log('Relationships analysis result:', result);
            
            // Update the relationships in the analysis
            this.currentAnalysis.analysis.relationships = result.relationships;
            
            // Update UI
            document.getElementById('step-relationships').classList.remove('active');
            document.getElementById('step-relationships').classList.add('completed');
            this.populateRelationshipsTab(result.relationships);
            const relationshipCount = result.relationships.items.length;
            this.relationshipsCount.textContent = relationshipCount;
            
            // Switch to relationships tab
            this.switchTab('relationships');
            
            // Update button state
            this.analyzeRelationshipsBtn.classList.remove('loading');
            this.analyzeRelationshipsBtn.classList.add('completed');
            this.analyzeRelationshipsBtn.innerHTML = '✓ 2. Analyze Relationships';
            
            this.showStatus(`Successfully analyzed ${relationshipCount} relationships between actors`, 'success');
            
            // Enable the next step button
            this.detectPortrayalsBtn.disabled = false;
        } catch (error) {
            console.error('Relationship analysis error:', error);
            this.showStatus('Relationship analysis failed: ' + error.message, 'error');
            
            // Remove active state from progress indicator
            document.getElementById('step-relationships').classList.remove('active');
            
            // Reset analysis state for this step
            if (this.currentAnalysis && this.currentAnalysis.analysis) {
                this.currentAnalysis.analysis.relationships = { items: [] };
            }
        } finally {
            // Reset button state
            this.analyzeRelationshipsBtn.disabled = false;
            this.analyzeRelationshipsBtn.classList.remove('loading');
            this.analyzeRelationshipsBtn.innerHTML = '2. Analyze Relationships';
        }
    }
    
    // Step 3: Detect Portrayals
    async detectPortrayals() {
        if (!this.currentAnalysis || !this.currentAnalysis.analysis.actors) {
            this.showStatus('Please extract actors first', 'error');
            return;
        }
        
        // Check if we have relationships
        if (!this.currentAnalysis.analysis.relationships || !this.currentAnalysis.analysis.relationships.items) {
            this.showStatus('Please analyze relationships first', 'error');
            return;
        }
        
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        // Add actors and relationships to the request
        analysisData.actors = this.currentAnalysis.analysis.actors;
        analysisData.relationships = this.currentAnalysis.analysis.relationships;
        
        // Disable button and show loading state
        this.detectPortrayalsBtn.disabled = true;
        this.detectPortrayalsBtn.innerHTML = '<span class="loading"></span> Detecting...';
        this.detectPortrayalsBtn.classList.add('loading');
        
        // Update progress indicator
        document.getElementById('step-portrayals').classList.add('active');
        document.getElementById('step-portrayals').classList.remove('completed');
        
        // Show status message
        this.showStatus('Detecting portrayals of actors...', 'info');
        
        try {
            console.log('Detecting portrayals with data:', analysisData);
            const response = await fetch('/api/advanced/detect-portrayals', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, ${errorText}`);
            }

            const result = await response.json();
            console.log('Portrayals detection result:', result);
            
            // Update the portrayals in the analysis
            this.currentAnalysis.analysis.portrayals = result.portrayals;
            
            // Update UI
            document.getElementById('step-portrayals').classList.remove('active');
            document.getElementById('step-portrayals').classList.add('completed');
            this.populatePortrayalsTab(result.portrayals);
            const portrayalsCount = result.portrayals.items.length;
            this.portrayalsCount.textContent = portrayalsCount;
            
            // Switch to portrayals tab
            this.switchTab('portrayals');
            
            // Update button state
            this.detectPortrayalsBtn.classList.remove('loading');
            this.detectPortrayalsBtn.classList.add('completed');
            this.detectPortrayalsBtn.innerHTML = '✓ 3. Detect Portrayals';
            
            this.showStatus(`Successfully detected ${portrayalsCount} portrayals of actors`, 'success');
            
            // Enable the next step button
            this.analyzeIssueScopeBtn.disabled = false;
        } catch (error) {
            console.error('Portrayals detection error:', error);
            this.showStatus('Portrayals detection failed: ' + error.message, 'error');
            
            // Remove active state from progress indicator
            document.getElementById('step-portrayals').classList.remove('active');
            
            // Reset analysis state for this step
            if (this.currentAnalysis && this.currentAnalysis.analysis) {
                this.currentAnalysis.analysis.portrayals = { items: [] };
            }
        } finally {
            // Reset button state
            this.detectPortrayalsBtn.disabled = false;
            this.detectPortrayalsBtn.classList.remove('loading');
            this.detectPortrayalsBtn.innerHTML = '3. Detect Portrayals';
        }
    }
    
    // Step 4: Analyze Issue Scope
    async analyzeIssueScope() {
        if (!this.currentAnalysis || !this.currentAnalysis.analysis.actors) {
            this.showStatus('Please extract actors first', 'error');
            return;
        }
        
        // Check if we have portrayals
        if (!this.currentAnalysis.analysis.portrayals || !this.currentAnalysis.analysis.portrayals.items) {
            this.showStatus('Please detect portrayals first', 'error');
            return;
        }
        
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        // Add all required data to the request
        analysisData.actors = this.currentAnalysis.analysis.actors;
        analysisData.portrayals = this.currentAnalysis.analysis.portrayals;
        
        // Disable button and show loading state
        this.analyzeIssueScopeBtn.disabled = true;
        this.analyzeIssueScopeBtn.innerHTML = '<span class="loading"></span> Analyzing...';
        this.analyzeIssueScopeBtn.classList.add('loading');
        
        // Update progress indicator
        document.getElementById('step-scope').classList.add('active');
        document.getElementById('step-scope').classList.remove('completed');
        
        // Show status message
        this.showStatus('Analyzing issue scope...', 'info');
        
        try {
            console.log('Analyzing issue scope with data:', analysisData);
            const response = await fetch('/api/advanced/analyze-issue-scope', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, ${errorText}`);
            }

            const result = await response.json();
            console.log('Issue scope analysis result:', result);
            
            // Update the issue scope in the analysis
            this.currentAnalysis.analysis.issue_scope = result.issue_scope;
            
            // Update UI
            document.getElementById('step-scope').classList.remove('active');
            document.getElementById('step-scope').classList.add('completed');
            this.populateIssueScopeTab(result.issue_scope);
            
            // Switch to issue scope tab
            this.switchTab('issue-scope');
            
            // Update button state
            this.analyzeIssueScopeBtn.classList.remove('loading');
            this.analyzeIssueScopeBtn.classList.add('completed');
            this.analyzeIssueScopeBtn.innerHTML = '✓ 4. Analyze Issue Scope';
            
            this.showStatus('Issue scope analyzed successfully', 'success');
            
            // Enable the next step button
            this.detectCausalMechanismsBtn.disabled = false;
        } catch (error) {
            console.error('Issue scope analysis error:', error);
            this.showStatus('Issue scope analysis failed: ' + error.message, 'error');
            
            // Remove active state from progress indicator
            document.getElementById('step-scope').classList.remove('active');
            
            // Reset analysis state for this step
            if (this.currentAnalysis && this.currentAnalysis.analysis) {
                this.currentAnalysis.analysis.issue_scope = {};
            }
        } finally {
            // Reset button state
            this.analyzeIssueScopeBtn.disabled = false;
            this.analyzeIssueScopeBtn.classList.remove('loading');
            this.analyzeIssueScopeBtn.innerHTML = '4. Analyze Issue Scope';
        }
    }
    
    // Step 5: Detect Causal Mechanisms
    async detectCausalMechanisms() {
        if (!this.currentAnalysis || !this.currentAnalysis.analysis.actors) {
            this.showStatus('Please extract actors first', 'error');
            return;
        }
        
        // Check if we have issue scope
        if (!this.currentAnalysis.analysis.issue_scope || Object.keys(this.currentAnalysis.analysis.issue_scope).length === 0) {
            this.showStatus('Please analyze issue scope first', 'error');
            return;
        }
        
        const analysisData = this.getAnalysisData();
        if (!analysisData) return;
        
        // Add all required data to the request
        analysisData.actors = this.currentAnalysis.analysis.actors;
        analysisData.issue_scope = this.currentAnalysis.analysis.issue_scope;
        
        // Disable button and show loading state
        this.detectCausalMechanismsBtn.disabled = true;
        this.detectCausalMechanismsBtn.innerHTML = '<span class="loading"></span> Detecting...';
        this.detectCausalMechanismsBtn.classList.add('loading');
        
        // Update progress indicator
        document.getElementById('step-causal').classList.add('active');
        document.getElementById('step-causal').classList.remove('completed');
        
        // Show status message
        this.showStatus('Detecting causal mechanisms...', 'info');
        
        try {
            console.log('Detecting causal mechanisms with data:', analysisData);
            const response = await fetch('/api/advanced/detect-causal-mechanisms', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, ${errorText}`);
            }

            const result = await response.json();
            console.log('Causal mechanisms detection result:', result);
            
            // Update the causal mechanisms in the analysis
            this.currentAnalysis.analysis.causal_mechanisms = result.causal_mechanisms;
            
            // Update UI
            document.getElementById('step-causal').classList.remove('active');
            document.getElementById('step-causal').classList.add('completed');
            this.populateCausalMechanismsTab(result.causal_mechanisms);
            const mechanismsCount = result.causal_mechanisms.items ? result.causal_mechanisms.items.length : 0;
            
            // Update button state
            this.detectCausalMechanismsBtn.classList.remove('loading');
            this.detectCausalMechanismsBtn.classList.add('completed');
            this.detectCausalMechanismsBtn.innerHTML = '✓ 5. Detect Causal Mechanisms';
            
            // Switch to causal mechanisms tab
            this.switchTab('causal-mechanisms');
            
            this.showStatus('Causal mechanisms detected successfully', 'success');
            
            // Mark analysis as complete by simulating the final step
            document.getElementById('step-decisions').classList.add('completed');
            
            // Calculate quality score if we have all components
            if (this.currentAnalysis.analysis.actors && 
                this.currentAnalysis.analysis.relationships && 
                this.currentAnalysis.analysis.portrayals) {
                    
                // Simple quality calculation based on count of items
                const actorsScore = Math.min(this.currentAnalysis.analysis.actors.length / 10, 1);
                const relsScore = Math.min(this.currentAnalysis.analysis.relationships.items.length / 15, 1);
                const portrayalScore = Math.min(this.currentAnalysis.analysis.portrayals.items.length / 15, 1);
                
                const qualityScore = (actorsScore + relsScore + portrayalScore) / 3;
                this.qualityScore.textContent = Math.round(qualityScore * 100) + '%';
                
                if (!this.currentAnalysis.quality) {
                    this.currentAnalysis.quality = { score: qualityScore };
                }
            }
        } catch (error) {
            console.error('Causal mechanisms detection error:', error);
            this.showStatus('Causal mechanisms detection failed: ' + error.message, 'error');
            
            // Remove active state from progress indicator
            document.getElementById('step-causal').classList.remove('active');
            
            // Reset analysis state for this step
            if (this.currentAnalysis && this.currentAnalysis.analysis) {
                this.currentAnalysis.analysis.causal_mechanisms = { items: [] };
            }
            
            // Remove the completed marker from the last step
            document.getElementById('step-decisions').classList.remove('completed');
        } finally {
            // Reset button state
            this.detectCausalMechanismsBtn.disabled = false;
            this.detectCausalMechanismsBtn.classList.remove('loading');
            this.detectCausalMechanismsBtn.innerHTML = '5. Detect Causal Mechanisms';
        }
    }

    initializeStepButtonsState() {
        // Always enable the first step (Extract Actors)
        this.extractActorsBtn.disabled = false;
        this.extractActorsBtn.classList.remove('loading', 'completed', 'active');
        this.extractActorsBtn.innerHTML = '1. Extract Actors';
        
        // Disable all other steps initially
        this.analyzeRelationshipsBtn.disabled = true;
        this.analyzeRelationshipsBtn.classList.remove('loading', 'completed', 'active');
        this.analyzeRelationshipsBtn.innerHTML = '2. Analyze Relationships';
        
        this.detectPortrayalsBtn.disabled = true;
        this.detectPortrayalsBtn.classList.remove('loading', 'completed', 'active');
        this.detectPortrayalsBtn.innerHTML = '3. Detect Portrayals';
        
        this.analyzeIssueScopeBtn.disabled = true;
        this.analyzeIssueScopeBtn.classList.remove('loading', 'completed', 'active');
        this.analyzeIssueScopeBtn.innerHTML = '4. Analyze Issue Scope';
        
        this.detectCausalMechanismsBtn.disabled = true;
        this.detectCausalMechanismsBtn.classList.remove('loading', 'completed', 'active');
        this.detectCausalMechanismsBtn.innerHTML = '5. Detect Causal Mechanisms';
        
        // Disable reset button until there's an analysis to reset
        if (this.resetBtn) {
            this.resetBtn.disabled = !this.currentAnalysis;
        }
    }
    
    resetProgress() {
        this.analysisProgress = 0;
        this.progressFill.style.width = '0%';
        
        this.progressSteps.forEach((stepId, index) => {
            const step = document.getElementById(stepId);
            step.classList.remove('active', 'completed');
        });
        
        // Reset step button states
        this.initializeStepButtonsState();
    }
    
    resetAnalysis() {
        // Ask for confirmation
        if (!confirm('Are you sure you want to reset the current analysis? This will clear all results.')) {
            return;
        }
        
        // Clear analysis state
        this.currentAnalysis = null;
        
        // Reset progress
        this.resetProgress();
        
        // Clear results in UI tabs
        document.getElementById('actors-grid').innerHTML = '';
        document.getElementById('relationships-content').innerHTML = '';
        document.getElementById('portrayals-content').innerHTML = '';
        document.getElementById('issue-scope-content').innerHTML = '';
        document.getElementById('causal-content').innerHTML = '';
        
        // Reset summary cards
        document.getElementById('actor-count').textContent = '0';
        document.getElementById('relationship-count').textContent = '0';
        document.getElementById('portrayal-count').textContent = '0';
        document.getElementById('quality-score').textContent = '0';
        
        // Reset status message
        this.statusMessage.textContent = 'Analysis reset. Ready for new analysis.';
        this.statusMessage.className = 'status-message';
        
        // Reset all step button states
        this.initializeStepButtonsState();
        
        // Re-enable main analyze button
        this.analyzeBtn.disabled = false;
        this.analyzeBtn.textContent = 'Analyze Document';
        
        console.log('Analysis reset. All state cleared.');
    }
    
    initializeStepButtonsState() {
        // Initially only enable the first button
        this.extractActorsBtn.disabled = false;
        this.analyzeRelationshipsBtn.disabled = true;
        this.detectPortrayalsBtn.disabled = true;
        this.analyzeIssueScopeBtn.disabled = true;
        this.detectCausalMechanismsBtn.disabled = true;
        
        // If we have analysis data, enable buttons accordingly
        if (this.currentAnalysis && this.currentAnalysis.analysis) {
            const analysis = this.currentAnalysis.analysis;
            
            if (analysis.actors && analysis.actors.length > 0) {
                this.analyzeRelationshipsBtn.disabled = false;
                document.getElementById('step-actors').classList.add('completed');
                
                if (analysis.relationships && analysis.relationships.items) {
                    this.detectPortrayalsBtn.disabled = false;
                    document.getElementById('step-relationships').classList.add('completed');
                    
                    if (analysis.portrayals && analysis.portrayals.items) {
                        this.analyzeIssueScopeBtn.disabled = false;
                        document.getElementById('step-portrayals').classList.add('completed');
                        
                        if (analysis.issue_scope) {
                            this.detectCausalMechanismsBtn.disabled = false;
                            document.getElementById('step-scope').classList.add('completed');
                            
                            if (analysis.causal_mechanisms && analysis.causal_mechanisms.items) {
                                document.getElementById('step-causal').classList.add('completed');
                                document.getElementById('step-decisions').classList.add('completed');
                            }
                        }
                    }
                }
            }
        }
    }

    async simulateProgress() {
        const steps = [
            { name: 'step-actors', duration: 1000 },
            { name: 'step-relationships', duration: 1500 },
            { name: 'step-portrayals', duration: 1200 },
            { name: 'step-scope', duration: 1000 },
            { name: 'step-causal', duration: 1200 },
            { name: 'step-decisions', duration: 800 }
        ];

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            
            // Mark current step as active
            document.getElementById(step.name).classList.add('active');
            
            // Wait for step duration
            await new Promise(resolve => setTimeout(resolve, step.duration));
            
            // Mark step as completed
            document.getElementById(step.name).classList.remove('active');
            document.getElementById(step.name).classList.add('completed');
            
            // Update progress bar
            this.analysisProgress = ((i + 1) / steps.length) * 100;
            this.progressFill.style.width = this.analysisProgress + '%';
        }
    }

    displayResults(result) {
        console.log('📊 Displaying results:', result);

        // Hide progress, show results
        this.progressSection.classList.add('hidden');
        this.resultsSection.classList.remove('hidden');

        // Update summary cards
        this.updateSummaryCards(result);

        // Populate tab content with null checks
        if (result.analysis) {
            this.populateActorsTab(result.analysis.actors || []);
            this.populateRelationshipsTab(result.analysis.relationships || { items: [] });
            this.populatePortrayalsTab(result.analysis.portrayals || { items: [] });
            this.populateIssueScopeTab(result.analysis.issue_scope || { items: [] });
            this.populateCausalMechanismsTab(result.analysis.causal_mechanisms || { items: [] });
            this.populateRawResponsesTab(result.analysis);
        } else {
            console.warn('⚠️ No analysis data found in result');
        }

        this.populateMetadataTab(result.metadata || {});

        // Switch to first tab
        this.switchTab('actors');
    }

    updateSummaryCards(result) {
        // Safely update summary cards with null checks
        this.actorsCount.textContent = result.analysis?.actors?.length || 0;
        this.relationshipsCount.textContent = result.analysis?.relationships?.items?.length || 0;
        this.portrayalsCount.textContent = result.analysis?.portrayals?.items?.length || 0;
        this.qualityScore.textContent = Math.round(result.quality.score * 100) + '%';
    }

    populateActorsTab(actors) {
        const grid = document.getElementById('actorsGrid');
        grid.innerHTML = '';

        // Check if actors exists and is an array
        if (!actors || !Array.isArray(actors)) {
            grid.innerHTML = '<p>No actors data available</p>';
            return;
        }

        actors.forEach(actor => {
            const card = document.createElement('div');
            card.className = 'actor-card fade-in';
            card.innerHTML = `
                <h4>${actor.name}</h4>
                <div class="actor-category">${actor.stakeholder_category}</div>
                ${actor.notes ? `<div class="actor-notes">${actor.notes}</div>` : ''}
            `;
            grid.appendChild(card);
        });
    }

    populateRelationshipsTab(relationships) {
        const network = document.getElementById('relationshipsNetwork');
        network.innerHTML = '';

        // Check if relationships exists and has items
        if (!relationships || !relationships.items || !Array.isArray(relationships.items)) {
            network.innerHTML = '<p>No relationships data available</p>';
            return;
        }

        relationships.items.forEach(rel => {
            const card = document.createElement('div');
            card.className = 'relationship-card fade-in';
            card.innerHTML = `
                <div class="relationship-header">
                    <div class="relationship-actors">${rel.a_name} → ${rel.b_name}</div>
                    <div class="relationship-type">${rel.relationship}</div>
                </div>
                <div class="character-tags">
                    ${rel.a_character ? `<span class="character-tag character-${rel.a_character}">${rel.a_name}: ${rel.a_character}</span>` : ''}
                    ${rel.b_character ? `<span class="character-tag character-${rel.b_character}">${rel.b_name}: ${rel.b_character}</span>` : ''}
                </div>
                <div class="relationship-evidence">"${rel.evidence}"</div>
            `;
            network.appendChild(card);
        });
    }

    populatePortrayalsTab(portrayals) {
        const list = document.getElementById('portrayalsList');
        list.innerHTML = '';

        // Check if portrayals exists and has items
        if (!portrayals || !portrayals.items || !Array.isArray(portrayals.items)) {
            list.innerHTML = '<p>No portrayals data available</p>';
            return;
        }

        portrayals.items.forEach(portrayal => {
            const card = document.createElement('div');
            card.className = 'portrayal-card fade-in';
            card.innerHTML = `
                <div class="portrayal-type portrayal-${portrayal.type.toLowerCase()}">${portrayal.type}</div>
                <h4>${portrayal.actor}</h4>
                <div class="portrayal-evidence">"${portrayal.evidence}"</div>
                <div class="portrayal-explanation">${portrayal.explanation}</div>
            `;
            list.appendChild(card);
        });

        // Add narrative shifts if present
        if (portrayals.shifts.angel_shift_present || portrayals.shifts.devil_shift_present) {
            const shiftsCard = document.createElement('div');
            shiftsCard.className = 'portrayal-card fade-in';
            shiftsCard.innerHTML = `
                <h4>Narrative Shifts Detected</h4>
                ${portrayals.shifts.angel_shift_present ? `<div class="portrayal-type portrayal-hero">Angel Shift Present</div><div class="portrayal-evidence">"${portrayals.shifts.angel_shift_evidence}"</div>` : ''}
                ${portrayals.shifts.devil_shift_present ? `<div class="portrayal-type portrayal-devil">Devil Shift Present</div><div class="portrayal-evidence">"${portrayals.shifts.devil_shift_evidence}"</div>` : ''}
            `;
            list.appendChild(shiftsCard);
        }
    }

    populateIssueScopeTab(issueScope) {
        const list = document.getElementById('issueScopeList');
        list.innerHTML = '';

        // Check if issueScope exists and has items
        if (!issueScope || !issueScope.items || !Array.isArray(issueScope.items)) {
            list.innerHTML = '<p>No issue scope data available</p>';
            return;
        }

        issueScope.items.forEach(item => {
            const card = document.createElement('div');
            card.className = 'scope-card fade-in';
            card.innerHTML = `
                <div class="scope-type scope-${item.type.toLowerCase().replace(' ', '-')}">${item.type}</div>
                <h4>${item.actor}</h4>
                <div class="portrayal-evidence">"${item.evidence}"</div>
                <div class="portrayal-explanation">${item.explanation}</div>
            `;
            list.appendChild(card);
        });
    }

    populateCausalMechanismsTab(causalMechanisms) {
        const list = document.getElementById('causalMechanismsList');
        list.innerHTML = '';

        // Check if causalMechanisms exists and has items
        if (!causalMechanisms || !causalMechanisms.items || !Array.isArray(causalMechanisms.items)) {
            list.innerHTML = '<p>No causal mechanisms data available</p>';
            return;
        }

        causalMechanisms.items.forEach(item => {
            const card = document.createElement('div');
            card.className = 'causal-card fade-in';
            card.innerHTML = `
                <div class="causal-type causal-${item.type.toLowerCase()}">${item.type}</div>
                <h4>${item.actor}</h4>
                <div class="portrayal-evidence">"${item.evidence}"</div>
                <div class="portrayal-explanation">${item.explanation}</div>
            `;
            list.appendChild(card);
        });
    }

    populateMetadataTab(metadata) {
        const info = document.getElementById('metadataInfo');

        if (!metadata || typeof metadata !== 'object') {
            info.innerHTML = '<p>No metadata available</p>';
            return;
        }

        info.innerHTML = `
            <div class="metadata-grid">
                <div class="metadata-item">
                    <span class="metadata-label">Document ID:</span>
                    <span class="metadata-value">${metadata.doc_id || 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Title:</span>
                    <span class="metadata-value">${metadata.title || 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Analysis Time:</span>
                    <span class="metadata-value">${metadata.analysis_timestamp ? new Date(metadata.analysis_timestamp).toLocaleString() : (metadata.timestamp ? metadata.timestamp : 'N/A')}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Text Length:</span>
                    <span class="metadata-value">${metadata.text_length ? metadata.text_length.toLocaleString() + ' characters' : 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Known Actors:</span>
                    <span class="metadata-value">${metadata.known_actors_count || 0}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Total Actors Found:</span>
                    <span class="metadata-value">${metadata.total_actors_found || 0}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Analysis Steps:</span>
                    <span class="metadata-value">${metadata.analysis_steps ? metadata.analysis_steps.join(', ') : 'N/A'}</span>
                </div>
            </div>
        `;
    }

    switchTab(tabName) {
        // Update tab buttons
        this.tabButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            }
        });

        // Update tab panes
        this.tabPanes.forEach(pane => {
            pane.classList.remove('active');
            if (pane.id === `${tabName}-tab`) {
                pane.classList.add('active');
            }
        });
    }

    async loadConfigurationFromServer() {
        this.loadConfigBtn.innerHTML = '<span class="loading"></span> Loading...';
        this.loadConfigBtn.disabled = true;

        try {
            const response = await fetch('/api/config');
            const serverConfig = await response.json();

            if (serverConfig && serverConfig.llm) {
                const config = {
                    provider: serverConfig.llm.provider || 'zhipu',
                    model: serverConfig.llm.model,
                    apiKey: serverConfig.llm.apiKey,
                    baseUrl: serverConfig.llm.baseUrl,
                    useRealLLM: true
                };

                this.applyConfiguration(config);
                this.updateConfigurationStatus('Loaded from server', config.apiKey);
                this.showStatus('Configuration loaded successfully from server', 'success');
            } else {
                this.showStatus('No configuration found on server', 'warning');
                this.updateConfigurationStatus('No server config', null, 'warning');
            }
        } catch (error) {
            console.error('Error loading configuration from server:', error);
            this.showStatus('Failed to load configuration from server: ' + error.message, 'error');
            this.updateConfigurationStatus('Load failed', null, 'error');
        } finally {
            this.loadConfigBtn.innerHTML = 'Load Configuration';
            this.loadConfigBtn.disabled = false;
        }
    }

    updateConfigurationStatus(status, apiKey, type = 'success') {
        // Update configuration status text
        this.configStatusText.textContent = status;
        this.configStatusText.className = `status-value ${type}`;

        // Update API key status
        if (apiKey) {
            this.apiKeyStatus.textContent = `Set (${apiKey.substring(0, 8)}...)`;
            this.apiKeyStatus.className = 'status-value success';
        } else {
            this.apiKeyStatus.textContent = 'Not set';
            this.apiKeyStatus.className = 'status-value error';
        }

        // Reset validation status when configuration changes
        this.validationStatus.textContent = 'Not validated';
        this.validationStatus.className = 'status-value';

        // Update last updated time
        this.configLastUpdated.textContent = new Date().toLocaleString();
        this.configLastUpdated.className = 'status-value';
    }

    updateRequestStatus(step, status, responseTime = null, progress = null) {
        // Update current step
        if (step) {
            this.currentStep.textContent = step;
            this.currentStep.className = 'status-value active';
        }

        // Update request status
        if (status) {
            this.requestStatus.textContent = status;
            let statusClass = 'status-value';

            if (status.toLowerCase().includes('success') || status.toLowerCase().includes('complete')) {
                statusClass += ' success';
            } else if (status.toLowerCase().includes('error') || status.toLowerCase().includes('failed')) {
                statusClass += ' error';
            } else if (status.toLowerCase().includes('processing') || status.toLowerCase().includes('sending')) {
                statusClass += ' active';
            }

            this.requestStatus.className = statusClass;
        }

        // Update response time
        if (responseTime !== null) {
            this.responseTime.textContent = `${responseTime}ms`;
            this.responseTime.className = 'status-value';
        }

        // Update progress
        if (progress !== null) {
            this.progressPercent.textContent = `${progress}%`;
            this.progressPercent.className = 'status-value';

            // Update progress bar if it exists
            if (this.progressFill) {
                this.progressFill.style.width = `${progress}%`;
            }
        }
    }

    resetRequestStatus() {
        this.currentStep.textContent = 'Ready';
        this.currentStep.className = 'status-value';
        this.requestStatus.textContent = 'Idle';
        this.requestStatus.className = 'status-value';
        this.responseTime.textContent = '-';
        this.responseTime.className = 'status-value';
        this.progressPercent.textContent = '0%';
        this.progressPercent.className = 'status-value';

        if (this.progressFill) {
            this.progressFill.style.width = '0%';
        }
    }

    showErrorDetails(error, context = {}) {
        // Determine error type and suggestions
        const errorInfo = this.analyzeError(error, context);

        // Populate error details
        this.errorMessage.textContent = errorInfo.message;
        this.errorMessage.className = errorInfo.critical ? 'error-text critical' : 'error-text';

        this.errorType.textContent = errorInfo.type;
        this.errorTimestamp.textContent = new Date().toLocaleString();
        this.errorRequestDetails.textContent = errorInfo.requestDetails;
        this.errorSuggestions.textContent = errorInfo.suggestions;

        // Show error panel with overlay
        this.createErrorOverlay();
        this.errorDetails.classList.remove('hidden');

        // Log error for debugging
        console.error('Detailed error:', {
            error,
            context,
            errorInfo
        });
    }

    hideErrorDetails() {
        this.errorDetails.classList.add('hidden');
        this.removeErrorOverlay();
    }

    createErrorOverlay() {
        let overlay = document.querySelector('.error-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'error-overlay';
            document.body.appendChild(overlay);
        }
        overlay.classList.add('show');
    }

    removeErrorOverlay() {
        const overlay = document.querySelector('.error-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    analyzeError(error, context) {
        const errorInfo = {
            message: error.message || 'Unknown error occurred',
            type: 'Unknown Error',
            critical: false,
            requestDetails: 'No request details available',
            suggestions: 'Please try again or contact support'
        };

        // Analyze error type and provide specific guidance
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorInfo.type = 'Network Error';
            errorInfo.critical = true;
            errorInfo.suggestions = '1. Check your internet connection\n2. Verify the server is running\n3. Check if the API endpoint is correct';
        } else if (error.message.includes('HTTP error')) {
            const statusMatch = error.message.match(/status: (\d+)/);
            const status = statusMatch ? statusMatch[1] : 'unknown';

            errorInfo.type = `HTTP Error (${status})`;
            errorInfo.critical = true;

            switch (status) {
                case '400':
                    errorInfo.suggestions = '1. Check your input data format\n2. Verify all required fields are filled\n3. Ensure document text is not too long';
                    break;
                case '401':
                    errorInfo.suggestions = '1. Check your API key is correct\n2. Verify API key has proper permissions\n3. Try reloading configuration';
                    break;
                case '403':
                    errorInfo.suggestions = '1. Check API key permissions\n2. Verify account has access to this service\n3. Contact your API provider';
                    break;
                case '429':
                    errorInfo.suggestions = '1. You have exceeded rate limits\n2. Wait a few minutes before trying again\n3. Consider upgrading your API plan';
                    break;
                case '500':
                    errorInfo.suggestions = '1. Server error occurred\n2. Try again in a few minutes\n3. Contact support if problem persists';
                    break;
                default:
                    errorInfo.suggestions = '1. Check server status\n2. Verify your request parameters\n3. Try again later';
            }
        } else if (error.message.includes('API key')) {
            errorInfo.type = 'Configuration Error';
            errorInfo.critical = true;
            errorInfo.suggestions = '1. Check your API key is set correctly\n2. Verify API key format\n3. Try loading configuration from server';
        } else if (error.message.includes('timeout')) {
            errorInfo.type = 'Timeout Error';
            errorInfo.suggestions = '1. Check your internet connection\n2. Try with a shorter document\n3. Increase timeout settings if possible';
        }

        // Add request context details
        if (context.url) {
            errorInfo.requestDetails = `URL: ${context.url}`;
        }
        if (context.method) {
            errorInfo.requestDetails += `\nMethod: ${context.method}`;
        }
        if (context.data) {
            errorInfo.requestDetails += `\nData: ${JSON.stringify(context.data, null, 2).substring(0, 200)}...`;
        }

        return errorInfo;
    }

    // Enhanced error handling for existing methods
    handleError(error, context = {}) {
        // Show basic status message
        this.showStatus(`Error: ${error.message}`, 'error');

        // Update request status
        this.updateRequestStatus('Error occurred', `Error: ${error.message}`, null, 0);

        // Show detailed error information
        this.showErrorDetails(error, context);
    }

    async validateConfiguration() {
        this.validateConfigBtn.innerHTML = '<span class="loading"></span> Validating...';
        this.validateConfigBtn.disabled = true;

        try {
            // Get current configuration
            const config = {
                provider: this.providerSelect.value,
                model: this.modelSelect.value,
                apiKey: this.apiKeyInput.value,
                baseUrl: this.baseUrlInput.value
            };

            // Perform client-side validation
            const clientValidation = this.performClientSideValidation(config);

            if (!clientValidation.valid) {
                this.updateValidationStatus('Client validation failed', 'error');
                this.showStatus(`Validation failed: ${clientValidation.errors.join(', ')}`, 'error');
                return;
            }

            // Perform server-side validation
            this.updateRequestStatus('Validating config', 'Sending to server', null, 25);

            const response = await fetch('/api/validate-config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (result.valid) {
                this.updateValidationStatus('Valid', 'success');
                this.updateRequestStatus('Validation complete', 'Success', null, 100);
                this.showStatus('Configuration is valid!', 'success');

                // Show validation details
                if (result.details) {
                    console.log('Validation details:', result.details);
                }
            } else {
                this.updateValidationStatus('Invalid', 'error');
                this.updateRequestStatus('Validation failed', 'Invalid config', null, 0);
                this.showStatus(`Configuration is invalid: ${result.errors.join(', ')}`, 'error');
            }

        } catch (error) {
            console.error('Error validating configuration:', error);
            this.updateValidationStatus('Validation error', 'error');
            this.handleError(error, {
                url: '/api/validate-config',
                method: 'POST'
            });
        } finally {
            this.validateConfigBtn.innerHTML = 'Validate Configuration';
            this.validateConfigBtn.disabled = false;
        }
    }

    performClientSideValidation(config) {
        const errors = [];

        // Check provider
        if (!config.provider) {
            errors.push('Provider is required');
        }

        // Check model
        if (!config.model) {
            errors.push('Model is required');
        }

        // Check API key
        if (!config.apiKey || config.apiKey.trim() === '') {
            errors.push('API key is required');
        } else if (config.apiKey.startsWith('***')) {
            errors.push('API key appears to be masked');
        }

        // Check API key format based on provider
        if (config.apiKey && !config.apiKey.startsWith('***')) {
            switch (config.provider) {
                case 'zhipu':
                    if (!config.apiKey.includes('.')) {
                        errors.push('Zhipu API key should contain a dot (.)');
                    }
                    break;
                case 'openai':
                    if (!config.apiKey.startsWith('sk-')) {
                        errors.push('OpenAI API key should start with "sk-"');
                    }
                    break;
                case 'anthropic':
                    if (!config.apiKey.startsWith('sk-ant-')) {
                        errors.push('Anthropic API key should start with "sk-ant-"');
                    }
                    break;
            }
        }

        // Check base URL format if provided
        if (config.baseUrl && config.baseUrl.trim() !== '') {
            try {
                new URL(config.baseUrl);
            } catch (e) {
                errors.push('Base URL is not a valid URL');
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    updateValidationStatus(status, type = 'success') {
        this.validationStatus.textContent = status;
        this.validationStatus.className = `status-value ${type}`;
    }

    // Debug Panel Methods
    initializeDebugPanel() {
        this.logDebug('Debug panel initialized', 'info');
        this.updateSystemStatus();
        this.updateConfigDebug();
    }

    toggleDebugPanel() {
        if (this.debugPanel.classList.contains('hidden')) {
            this.showDebugPanel();
        } else {
            this.hideDebugPanel();
        }
    }

    showDebugPanel() {
        this.debugPanel.classList.remove('hidden');
        this.updateSystemStatus();
        this.updateConfigDebug();
        this.logDebug('Debug panel opened', 'info');
    }

    hideDebugPanel() {
        this.debugPanel.classList.add('hidden');
        this.logDebug('Debug panel closed', 'info');
    }

    switchDebugTab(tabName) {
        // Update tab buttons
        this.debugTabButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            }
        });

        // Update tab panes
        this.debugTabPanes.forEach(pane => {
            pane.classList.remove('active');
            if (pane.id === `${tabName}-debug-tab`) {
                pane.classList.add('active');
            }
        });

        // Update content based on active tab
        switch (tabName) {
            case 'system':
                this.updateSystemStatus();
                break;
            case 'config':
                this.updateConfigDebug();
                break;
            case 'network':
                this.updateNetworkDebug();
                break;
        }
    }

    logDebug(message, level = 'info', details = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            details
        };

        this.debugLogsArray.push(logEntry);
        this.displayLogEntry(logEntry);

        // Keep only last 1000 log entries
        if (this.debugLogsArray.length > 1000) {
            this.debugLogsArray = this.debugLogsArray.slice(-1000);
        }
    }

    displayLogEntry(logEntry) {
        const logElement = document.createElement('div');
        logElement.className = `log-entry ${logEntry.level}`;

        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        logElement.innerHTML = `
            <div class="log-timestamp">${timestamp}</div>
            <div class="log-message">${logEntry.message}</div>
            ${logEntry.details ? `<div class="log-details">${JSON.stringify(logEntry.details, null, 2)}</div>` : ''}
        `;

        this.debugLogs.appendChild(logElement);

        // Auto-scroll if enabled
        if (this.autoScrollCheckbox.checked) {
            this.debugLogs.scrollTop = this.debugLogs.scrollHeight;
        }
    }

    clearDebugLogs() {
        this.debugLogsArray = [];
        this.debugLogs.innerHTML = '';
        this.logDebug('Debug logs cleared', 'info');
    }

    exportDebugLogs() {
        const logsText = this.debugLogsArray.map(log =>
            `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}${log.details ? '\n' + JSON.stringify(log.details, null, 2) : ''}`
        ).join('\n\n');

        const blob = new Blob([logsText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `debug-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.logDebug('Debug logs exported', 'info');
    }

    updateSystemStatus() {
        const browserInfo = document.getElementById('browserInfo');
        const appState = document.getElementById('appState');
        const performanceMetrics = document.getElementById('performanceMetrics');

        // Browser information
        browserInfo.textContent = `
User Agent: ${navigator.userAgent}
Language: ${navigator.language}
Platform: ${navigator.platform}
Cookies Enabled: ${navigator.cookieEnabled}
Online: ${navigator.onLine}
Screen Resolution: ${screen.width}x${screen.height}
Viewport: ${window.innerWidth}x${window.innerHeight}
        `.trim();

        // Application state
        appState.textContent = `
Current Provider: ${this.providerSelect.value}
Current Model: ${this.modelSelect.value}
API Key Set: ${!!this.apiKeyInput.value}
Analysis in Progress: ${this.analyzeBtn.disabled}
Debug Logs Count: ${this.debugLogsArray.length}
Network Requests Count: ${this.networkRequestsArray.length}
        `.trim();

        // Performance metrics
        if (performance && performance.memory) {
            performanceMetrics.textContent = `
Used JS Heap Size: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB
Total JS Heap Size: ${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB
JS Heap Size Limit: ${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB
            `.trim();
        } else {
            performanceMetrics.textContent = 'Performance metrics not available';
        }
    }

    updateConfigDebug() {
        const config = {
            provider: this.providerSelect.value,
            model: this.modelSelect.value,
            apiKey: this.apiKeyInput.value ? `${this.apiKeyInput.value.substring(0, 8)}...` : 'Not set',
            baseUrl: this.baseUrlInput.value,
            useRealLLM: true
        };

        this.currentConfig.textContent = JSON.stringify(config, null, 2);
    }

    updateNetworkDebug() {
        this.networkRequests.innerHTML = '';

        this.networkRequestsArray.forEach(request => {
            const requestElement = document.createElement('div');
            requestElement.className = 'network-request';

            const statusClass = request.status >= 200 && request.status < 300 ? 'success' : 'error';

            requestElement.innerHTML = `
                <div class="request-header">
                    <span class="request-method ${request.method}">${request.method}</span>
                    <span class="request-url">${request.url}</span>
                    <span class="request-status ${statusClass}">${request.status}</span>
                </div>
                <div class="request-details">
                    Duration: ${request.duration}ms |
                    Time: ${new Date(request.timestamp).toLocaleTimeString()}
                </div>
            `;

            this.networkRequests.appendChild(requestElement);
        });
    }

    logNetworkRequest(method, url, status, duration) {
        const request = {
            method,
            url,
            status,
            duration,
            timestamp: new Date().toISOString()
        };

        this.networkRequestsArray.push(request);

        // Keep only last 50 network requests
        if (this.networkRequestsArray.length > 50) {
            this.networkRequestsArray = this.networkRequestsArray.slice(-50);
        }

        this.logDebug(`${method} ${url} - ${status} (${duration}ms)`, status >= 200 && status < 300 ? 'success' : 'error');
    }

    // Prompt Template Management Methods
    onTemplateSelectionChange() {
        const selectedTemplate = this.templateSelect.value;
        this.templateTitle.textContent = `${selectedTemplate.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Template`;
        this.templateInfo.textContent = `Editing ${selectedTemplate} template`;
        this.templateContent.placeholder = `Enter the ${selectedTemplate} template content...`;
    }

    async loadSelectedTemplate() {
        const templateName = this.templateSelect.value;
        this.loadTemplateBtn.innerHTML = '<span class="loading"></span> Loading...';
        this.loadTemplateBtn.disabled = true;

        try {
            const response = await fetch(`/api/templates/${templateName}`);
            const result = await response.json();

            if (response.ok) {
                this.templateContent.value = result.template;
                this.showStatus(`Template ${templateName} loaded successfully`, 'success');
                this.logDebug(`Template loaded: ${templateName}`, 'info');
            } else {
                throw new Error(result.error || 'Failed to load template');
            }
        } catch (error) {
            console.error('Error loading template:', error);
            this.showStatus(`Failed to load template: ${error.message}`, 'error');
            this.logDebug(`Template load failed: ${error.message}`, 'error');
        } finally {
            this.loadTemplateBtn.innerHTML = 'Load Template';
            this.loadTemplateBtn.disabled = false;
        }
    }

    async saveCurrentTemplate() {
        const templateName = this.templateSelect.value;
        const templateContent = this.templateContent.value;

        if (!templateContent.trim()) {
            this.showStatus('Template content cannot be empty', 'error');
            return;
        }

        this.saveTemplateBtn.innerHTML = '<span class="loading"></span> Saving...';
        this.saveTemplateBtn.disabled = true;

        try {
            const response = await fetch(`/api/templates/${templateName}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ template: templateContent })
            });

            const result = await response.json();

            if (response.ok) {
                this.showStatus(`Template ${templateName} saved successfully`, 'success');
                this.logDebug(`Template saved: ${templateName}`, 'info');
            } else {
                throw new Error(result.error || 'Failed to save template');
            }
        } catch (error) {
            console.error('Error saving template:', error);
            this.showStatus(`Failed to save template: ${error.message}`, 'error');
            this.logDebug(`Template save failed: ${error.message}`, 'error');
        } finally {
            this.saveTemplateBtn.innerHTML = 'Save Template';
            this.saveTemplateBtn.disabled = false;
        }
    }

    async resetCurrentTemplate() {
        const templateName = this.templateSelect.value;

        if (!confirm(`Are you sure you want to reset the ${templateName} template to default? This will overwrite any custom changes.`)) {
            return;
        }

        this.resetTemplateBtn.innerHTML = '<span class="loading"></span> Resetting...';
        this.resetTemplateBtn.disabled = true;

        try {
            const response = await fetch(`/api/templates/${templateName}/reset`, {
                method: 'POST'
            });

            const result = await response.json();

            if (response.ok) {
                this.templateContent.value = result.template;
                this.showStatus(`Template ${templateName} reset to default`, 'success');
                this.logDebug(`Template reset: ${templateName}`, 'info');
            } else {
                throw new Error(result.error || 'Failed to reset template');
            }
        } catch (error) {
            console.error('Error resetting template:', error);
            this.showStatus(`Failed to reset template: ${error.message}`, 'error');
            this.logDebug(`Template reset failed: ${error.message}`, 'error');
        } finally {
            this.resetTemplateBtn.innerHTML = 'Reset to Default';
            this.resetTemplateBtn.disabled = false;
        }
    }
    
    async loadDefaultTemplate() {
        try {
            // If no template is selected, select the first one
            if (this.templateSelect.options.length > 0) {
                // Select the first template in the dropdown
                this.templateSelect.selectedIndex = 0;
                
                // Update template title and info based on selection
                this.onTemplateSelectionChange();
                
                // Load the selected template content
                await this.loadSelectedTemplate();
                
                this.logDebug('Default template loaded automatically', 'info');
            } else {
                this.logDebug('No templates available to load by default', 'warning');
            }
        } catch (error) {
            console.error('Error loading default template:', error);
            this.logDebug(`Failed to load default template: ${error.message}`, 'error');
        }
    }

    async previewCurrentTemplate() {
        const templateName = this.templateSelect.value;
        const templateContent = this.templateContent.value;

        if (!templateContent.trim()) {
            this.showStatus('Template content cannot be empty', 'error');
            return;
        }

        this.previewTemplateBtn.innerHTML = '<span class="loading"></span> Generating...';
        this.previewTemplateBtn.disabled = true;

        try {
            const response = await fetch('/api/templates/preview', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    templateName,
                    template: templateContent,
                    sampleData: {
                        text: "The FTC announced new regulations targeting Big Tech companies.",
                        title: "FTC Regulation Announcement",
                        doc_id: "sample_001",
                        actors: ["FTC", "Big Tech companies"],
                        relationships: [{"source": "FTC", "target": "Big Tech companies", "type": "regulates"}]
                    }
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.previewContent.textContent = result.preview;
                this.showStatus('Template preview generated', 'success');
                this.logDebug(`Template preview generated: ${templateName}`, 'info');
            } else {
                throw new Error(result.error || 'Failed to generate preview');
            }
        } catch (error) {
            console.error('Error generating preview:', error);
            this.showStatus(`Failed to generate preview: ${error.message}`, 'error');
            this.logDebug(`Template preview failed: ${error.message}`, 'error');
        } finally {
            this.previewTemplateBtn.innerHTML = 'Generate Preview';
            this.previewTemplateBtn.disabled = false;
        }
    }

    insertVariable(variable) {
        const textarea = this.templateContent;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;

        textarea.value = text.substring(0, start) + variable + text.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + variable.length, start + variable.length);
    }

    // Navigation Methods
    switchSection(sectionName) {
        // Update nav tabs
        this.navTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.section === sectionName);
        });

        // Update content sections
        this.contentSections.forEach(section => {
            section.classList.toggle('active', section.dataset.section === sectionName);
        });

        this.logDebug(`Switched to ${sectionName} section`, 'info');
    }

    updateConnectionStatus(connected, message = '') {
        const indicator = this.connectionIndicator;
        const dot = indicator.querySelector('.indicator-dot');
        const text = indicator.querySelector('.indicator-text');

        if (connected) {
            dot.classList.add('connected');
            text.textContent = message || 'Connected';
        } else {
            dot.classList.remove('connected');
            text.textContent = message || 'Disconnected';
        }
    }

    populateRawResponsesTab(analysis) {
        console.log('🔍 Populating Raw Responses Tab with analysis:', analysis);

        // Clear all responses first
        const responseElements = [
            'actorsPrompt', 'actorsRawResponse',
            'relationshipsPrompt', 'relationshipsRawResponse',
            'portrayalsPrompt', 'portrayalsRawResponse',
            'issueScopePrompt', 'issueScopeRawResponse',
            'causalMechanismsPrompt', 'causalMechanismsRawResponse'
        ];

        responseElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = 'No data available';
            }
        });

        // Populate actors raw response
        if (analysis.actors) {
            console.log('📋 Actors data:', analysis.actors);
            const actorsPrompt = document.getElementById('actorsPrompt');
            const actorsResponse = document.getElementById('actorsRawResponse');

            if (actorsPrompt) {
                let promptText = analysis.actors._prompt || analysis.actors.prompt || 'No prompt available';
                // Clean up prompt text if it contains document text
                if (promptText.includes('Document text:') && promptText.length > 1000) {
                    const parts = promptText.split('Document text:');
                    if (parts.length > 1) {
                        const beforeDoc = parts[0].trim();
                        const afterDoc = parts[1].split('Return ONLY')[1] || '';
                        promptText = beforeDoc + '\n\nDocument text: [Document content hidden for readability]\n\n' + (afterDoc ? 'Return ONLY' + afterDoc : '');
                    }
                }
                actorsPrompt.textContent = promptText;
            }
            if (actorsResponse) {
                actorsResponse.textContent = analysis.actors._rawLLMResponse || analysis.actors.rawResponse || analysis.actors._raw || 'No response available';
            }
        }

        // Populate relationships raw response
        if (analysis.relationships) {
            console.log('🔗 Relationships data:', analysis.relationships);
            const relationshipsPrompt = document.getElementById('relationshipsPrompt');
            const relationshipsResponse = document.getElementById('relationshipsRawResponse');

            if (relationshipsPrompt) {
                relationshipsPrompt.textContent = analysis.relationships._prompt || analysis.relationships.prompt || 'No prompt available';
            }
            if (relationshipsResponse) {
                relationshipsResponse.textContent = analysis.relationships._rawLLMResponse || analysis.relationships.rawResponse || analysis.relationships._raw || 'No response available';
            }
        }

        // Populate portrayals raw response
        if (analysis.portrayals) {
            console.log('🎭 Portrayals data:', analysis.portrayals);
            const portrayalsPrompt = document.getElementById('portrayalsPrompt');
            const portrayalsResponse = document.getElementById('portrayalsRawResponse');

            if (portrayalsPrompt) {
                portrayalsPrompt.textContent = analysis.portrayals._prompt || analysis.portrayals.prompt || 'No prompt available';
            }
            if (portrayalsResponse) {
                portrayalsResponse.textContent = analysis.portrayals._rawLLMResponse || analysis.portrayals.rawResponse || analysis.portrayals._raw || 'No response available';
            }
        }

        // Populate issue scope raw response
        if (analysis.issue_scope) {
            console.log('🌐 Issue scope data:', analysis.issue_scope);
            const issueScopePrompt = document.getElementById('issueScopePrompt');
            const issueScopeResponse = document.getElementById('issueScopeRawResponse');

            if (issueScopePrompt) {
                let promptText = analysis.issue_scope._prompt || analysis.issue_scope.prompt || 'No prompt available';
                // Clean up prompt text if it contains document text
                if (promptText.includes('Document text:') && promptText.length > 1000) {
                    const parts = promptText.split('Document text:');
                    if (parts.length > 1) {
                        const beforeDoc = parts[0].trim();
                        const afterDoc = parts[1].split('Return ONLY')[1] || parts[1].split('Actors to analyze:')[1] || '';
                        promptText = beforeDoc + '\n\nDocument text: [Document content hidden for readability]\n\n' + (afterDoc ? 'Return ONLY' + afterDoc : '');
                    }
                }
                issueScopePrompt.textContent = promptText;
            }
            if (issueScopeResponse) {
                issueScopeResponse.textContent = analysis.issue_scope._rawLLMResponse || analysis.issue_scope.rawResponse || analysis.issue_scope._raw || 'No response available';
            }
        }

        // Populate causal mechanisms raw response
        if (analysis.causal_mechanisms) {
            console.log('⚡ Causal mechanisms data:', analysis.causal_mechanisms);
            const causalMechanismsPrompt = document.getElementById('causalMechanismsPrompt');
            const causalMechanismsResponse = document.getElementById('causalMechanismsRawResponse');

            if (causalMechanismsPrompt) {
                let promptText = analysis.causal_mechanisms._prompt || analysis.causal_mechanisms.prompt || 'No prompt available';
                // Clean up prompt text if it contains document text
                if (promptText.includes('Document text:') && promptText.length > 1000) {
                    const parts = promptText.split('Document text:');
                    if (parts.length > 1) {
                        const beforeDoc = parts[0].trim();
                        const afterDoc = parts[1].split('Return ONLY')[1] || parts[1].split('Actors to analyze:')[1] || '';
                        promptText = beforeDoc + '\n\nDocument text: [Document content hidden for readability]\n\n' + (afterDoc ? 'Return ONLY' + afterDoc : '');
                    }
                }
                causalMechanismsPrompt.textContent = promptText;
            }
            if (causalMechanismsResponse) {
                causalMechanismsResponse.textContent = analysis.causal_mechanisms._rawLLMResponse || analysis.causal_mechanisms.rawResponse || analysis.causal_mechanisms._raw || 'No response available';
            }
        }
    }

    // Batch Processing Methods
    onFolderSelected() {
        const files = Array.from(this.folderInput.files);
        const txtFiles = files.filter(file => file.name.toLowerCase().endsWith('.txt'));

        this.selectedFiles = txtFiles;
        this.folderPath.textContent = txtFiles.length > 0 ? `${txtFiles.length} .txt files selected` : 'No .txt files found';
        this.fileCount.textContent = `${txtFiles.length} files`;

        this.startBatchBtn.disabled = txtFiles.length === 0;

        if (txtFiles.length > 0) {
            this.logDebug(`Selected ${txtFiles.length} .txt files for batch processing`, 'info');
        } else {
            this.logDebug('No .txt files found in selected folder', 'warning');
        }
    }

    async startBatchProcessing() {
        if (!this.selectedFiles || this.selectedFiles.length === 0) {
            this.showStatus('Please select a folder with .txt files first', 'error');
            return;
        }

        // Validate configuration before starting batch processing
        if (!this.apiKeyInput.value || this.apiKeyInput.value.trim() === '') {
            this.showStatus('Please configure API key before starting batch processing', 'error');
            return;
        }

        // Initialize batch processing state
        this.batchState = {
            files: [...this.selectedFiles],
            processed: 0,
            success: 0,
            failed: 0,
            results: [],
            startTime: Date.now(),
            paused: false,
            stopped: false
        };

        // Clear previous request monitor
        this.clearRequestMonitor();

        // Show progress section
        this.batchProgress.style.display = 'block';
        this.batchResults.style.display = 'none';

        // Update UI
        this.startBatchBtn.disabled = true;
        this.pauseBatchBtn.disabled = false;
        this.stopBatchBtn.disabled = false;

        // Update stats
        this.updateBatchStats();

        this.logDebug(`Starting batch processing of ${this.selectedFiles.length} files`, 'info');
        this.addToProcessingLog(`🚀 Starting batch processing of ${this.selectedFiles.length} files`);

        // Start processing
        await this.processBatchFiles();
    }

    async processBatchFiles() {
        const batchSize = parseInt(this.batchSize.value);

        while (this.batchState.processed < this.batchState.files.length && !this.batchState.stopped) {
            if (this.batchState.paused) {
                await new Promise(resolve => {
                    const checkPause = () => {
                        if (!this.batchState.paused || this.batchState.stopped) {
                            resolve();
                        } else {
                            setTimeout(checkPause, 100);
                        }
                    };
                    checkPause();
                });
            }

            if (this.batchState.stopped) break;

            // Get next batch of files
            const startIndex = this.batchState.processed;
            const endIndex = Math.min(startIndex + batchSize, this.batchState.files.length);
            const batch = this.batchState.files.slice(startIndex, endIndex);

            // Process batch
            await this.processBatch(batch);
        }

        // Finish processing
        this.finishBatchProcessing();
    }

    async processBatch(batch) {
        this.updateCurrentFiles(batch);

        // Process files one by one to ensure proper request monitoring
        for (const file of batch) {
            if (this.batchState.stopped || this.batchState.paused) {
                break;
            }

            try {
                const result = await this.processFile(file);
                this.batchState.success++;
                this.batchState.results.push({
                    file: file.name,
                    status: 'success',
                    result: result,
                    processingTime: result.processingTime || 0,
                    actors: result.analysis?.actors?.length || 0,
                    relationships: result.analysis?.relationships?.items?.length || 0
                });
                this.addToProcessingLog(`✅ ${file.name} - Success (${((result.processingTime || 0) / 1000).toFixed(1)}s)`);
            } catch (error) {
                this.batchState.failed++;
                this.batchState.results.push({
                    file: file.name,
                    status: 'error',
                    error: error.message || 'Unknown error',
                    processingTime: 0,
                    actors: 0,
                    relationships: 0
                });
                this.addToProcessingLog(`❌ ${file.name} - Error: ${error.message}`);
            }

            this.batchState.processed++;
            this.updateBatchStats();
        }
    }

    async processFile(file) {
        const startTime = Date.now();
        const requestId = this.requestCounter++;

        try {
            const text = await this.readFileAsText(file);

            // Check document length and handle long documents
            if (text.length > 50000) {
                if (this.splitLongDocuments && this.splitLongDocuments.checked) {
                    console.warn(`📄 Document ${file.name} is too long (${text.length} characters). Attempting to split...`);

                    // Try to split the document into chunks
                    const chunks = this.splitDocumentIntoChunks(text, 45000); // Leave some buffer
                    if (chunks.length > 1) {
                        console.log(`📄 Split document into ${chunks.length} chunks`);

                        // Process first chunk only for now (could be enhanced to process all chunks)
                        const truncatedText = chunks[0];
                        console.log(`📄 Processing first chunk (${truncatedText.length} characters)`);

                        // Update request data with truncated text
                        requestData.text = truncatedText;
                        requestData.doc_id = `${file.name.replace('.txt', '')}_chunk1`;
                        requestData.title = `${file.name} (Chunk 1 of ${chunks.length})`;

                        // Add note to processing log
                        this.addToProcessingLog(`📄 ${file.name} - Split into ${chunks.length} chunks, processing first chunk`);
                    } else {
                        throw new Error(`Document too long (${text.length} characters). Maximum 50,000 characters allowed.`);
                    }
                } else {
                    throw new Error(`Document too long (${text.length} characters). Maximum 50,000 characters allowed. Enable "Split long documents" option to process this file.`);
                }
            }

            // Get current configuration for batch processing
            const config = {
                provider: this.providerSelect.value,
                model: this.modelSelect.value,
                apiKey: this.apiKeyInput.value,
                baseUrl: this.baseUrlInput.value || this.providerConfig[this.providerSelect.value].baseUrl
            };

            const requestData = {
                doc_id: file.name.replace('.txt', ''),
                title: file.name,
                text: text,
                useRealLLM: true,
                provider: config.provider,
                model: config.model,
                apiKey: config.apiKey,
                baseUrl: config.baseUrl
            };

            // Add request to monitor
            this.addRequestToMonitor(requestId, 'POST', '/api/advanced-analyze', requestData, file.name);

            const response = await fetch('/api/advanced-analyze', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                let errorData;
                try {
                    errorData = await response.json();
                } catch {
                    errorData = { error: await response.text() };
                }
                this.updateRequestInMonitor(requestId, 'error', errorData, Date.now() - startTime);
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            const processingTime = Date.now() - startTime;

            // Update request monitor with success
            this.updateRequestInMonitor(requestId, 'success', result, processingTime);

            return {
                ...result,
                processingTime
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            // Update request monitor with error if not already updated
            if (this.activeRequests.has(requestId)) {
                this.updateRequestInMonitor(requestId, 'error', { error: error.message }, processingTime);
            }
            throw new Error(`Failed to process ${file.name}: ${error.message}`);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => {
                let text = e.target.result;

                // Check if text contains mostly question marks (encoding issue)
                const questionMarkRatio = (text.match(/\?/g) || []).length / text.length;
                if (questionMarkRatio > 0.3) {
                    console.warn(`⚠️ File ${file.name} may have encoding issues (${(questionMarkRatio * 100).toFixed(1)}% question marks)`);

                    // Try reading with different encoding
                    const reader2 = new FileReader();
                    reader2.onload = e2 => {
                        const text2 = e2.target.result;
                        const questionMarkRatio2 = (text2.match(/\?/g) || []).length / text2.length;

                        if (questionMarkRatio2 < questionMarkRatio) {
                            console.log(`✅ Better encoding found for ${file.name}`);
                            resolve(text2);
                        } else {
                            console.warn(`⚠️ Using original text for ${file.name} despite encoding issues`);
                            resolve(text);
                        }
                    };
                    reader2.onerror = () => resolve(text); // Fallback to original
                    reader2.readAsText(file, 'UTF-8');
                } else {
                    resolve(text);
                }
            };
            reader.onerror = e => reject(new Error('Failed to read file'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    updateBatchStats() {
        this.totalFiles.textContent = this.batchState.files.length;
        this.processedFiles.textContent = this.batchState.processed;
        this.successFiles.textContent = this.batchState.success;
        this.failedFiles.textContent = this.batchState.failed;
        this.remainingFiles.textContent = this.batchState.files.length - this.batchState.processed;

        const progress = (this.batchState.processed / this.batchState.files.length) * 100;
        this.batchProgressFill.style.width = progress + '%';
        this.batchProgressText.textContent = Math.round(progress) + '%';
    }

    updateCurrentFiles(files) {
        this.currentFiles.innerHTML = files.map(file =>
            `<div class="current-file">📄 ${file.name}</div>`
        ).join('');
    }

    addToProcessingLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}\n`;
        this.processingLog.textContent += logEntry;
        this.processingLog.scrollTop = this.processingLog.scrollHeight;
    }

    pauseBatchProcessing() {
        this.batchState.paused = !this.batchState.paused;
        this.pauseBatchBtn.textContent = this.batchState.paused ? 'Resume' : 'Pause';

        const action = this.batchState.paused ? 'Paused' : 'Resumed';
        this.addToProcessingLog(`⏸️ Batch processing ${action.toLowerCase()}`);
        this.logDebug(`Batch processing ${action.toLowerCase()}`, 'info');
    }

    stopBatchProcessing() {
        this.batchState.stopped = true;
        this.addToProcessingLog('🛑 Batch processing stopped by user');
        this.logDebug('Batch processing stopped by user', 'info');
        this.finishBatchProcessing();
    }

    finishBatchProcessing() {
        const totalTime = Date.now() - this.batchState.startTime;
        const avgTime = this.batchState.success > 0 ? totalTime / this.batchState.success : 0;

        // Update UI
        this.startBatchBtn.disabled = false;
        this.pauseBatchBtn.disabled = true;
        this.stopBatchBtn.disabled = true;
        this.downloadResultsBtn.disabled = false;

        // Show results
        this.batchResults.style.display = 'block';
        this.populateBatchResults(totalTime, avgTime);

        this.addToProcessingLog(`🏁 Batch processing completed in ${(totalTime / 1000).toFixed(1)}s`);
        this.logDebug(`Batch processing completed: ${this.batchState.success} success, ${this.batchState.failed} failed`, 'info');
    }

    populateBatchResults(totalTime, avgTime) {
        // Update summary cards
        document.getElementById('summarySuccess').textContent = this.batchState.success;
        document.getElementById('summaryFailed').textContent = this.batchState.failed;
        document.getElementById('summaryTime').textContent = (totalTime / 1000).toFixed(1) + 's';
        document.getElementById('summaryAvgTime').textContent = (avgTime / 1000).toFixed(1) + 's';

        // Populate results table
        this.resultsTableBody.innerHTML = this.batchState.results.map(result => `
            <tr>
                <td>${result.file}</td>
                <td><span class="file-status ${result.status}">${result.status}</span></td>
                <td>${(result.processingTime / 1000).toFixed(1)}s</td>
                <td>${result.result?.analysis?.actors?.length || 0}</td>
                <td>${result.result?.analysis?.relationships?.items?.length || 0}</td>
                <td class="file-actions">
                    ${result.status === 'success' ?
                        '<button class="btn-view" onclick="app.viewFileResult(\'' + result.file + '\')">View</button>' :
                        '<button class="btn-retry" onclick="app.retryFile(\'' + result.file + '\')">Retry</button>'
                    }
                </td>
            </tr>
        `).join('');
    }

    // Request Monitor Methods
    clearRequestMonitor() {
        this.activeRequests.clear();
        this.requestList.innerHTML = '<p>API requests will appear here during batch processing...</p>';
    }

    addRequestToMonitor(requestId, method, url, requestData, fileName) {
        const timestamp = new Date().toLocaleTimeString();

        // Store request info
        this.activeRequests.set(requestId, {
            method,
            url,
            requestData,
            fileName,
            timestamp,
            startTime: Date.now()
        });

        // Create request item element
        const requestItem = document.createElement('div');
        requestItem.className = 'request-item pending';
        requestItem.id = `request-${requestId}`;

        // Create a sanitized version of request data for display (hide sensitive info)
        const displayRequestData = { ...requestData };
        if (displayRequestData.apiKey) {
            displayRequestData.apiKey = '***HIDDEN***';
        }

        requestItem.innerHTML = `
            <div class="request-header">
                <span class="request-url">${method} ${url} (${fileName})</span>
                <span class="request-status pending">PENDING</span>
            </div>
            <div class="request-details">
                <div>Started: ${timestamp}</div>
                <div class="request-body">Request: ${JSON.stringify(displayRequestData, null, 2)}</div>
                <div class="request-timing">Duration: Processing...</div>
            </div>
        `;

        // Add to request list (remove placeholder if exists)
        if (this.requestList.querySelector('p')) {
            this.requestList.innerHTML = '';
        }
        this.requestList.appendChild(requestItem);

        // Auto-scroll to bottom
        this.requestList.scrollTop = this.requestList.scrollHeight;

        // Also log to console for developer tools debugging
        console.group(`🌐 API Request #${requestId}: ${method} ${url}`);
        console.log('📄 File:', fileName);
        console.log('📤 Request Data:', displayRequestData);
        console.log('⏰ Started at:', timestamp);
        console.groupEnd();
    }

    updateRequestInMonitor(requestId, status, responseData, duration) {
        const requestItem = document.getElementById(`request-${requestId}`);
        if (!requestItem) return;

        const requestInfo = this.activeRequests.get(requestId);
        if (!requestInfo) return;

        // Update status
        requestItem.className = `request-item ${status}`;

        const statusElement = requestItem.querySelector('.request-status');
        statusElement.className = `request-status ${status}`;
        statusElement.textContent = status.toUpperCase();

        // Add response data
        const detailsElement = requestItem.querySelector('.request-details');
        const responseDiv = document.createElement('div');
        responseDiv.className = 'request-response';

        // Truncate very long responses for display
        let displayResponse = responseData;
        const responseStr = JSON.stringify(responseData, null, 2);
        if (responseStr.length > 2000) {
            displayResponse = {
                ...responseData,
                _truncated: true,
                _originalLength: responseStr.length,
                _note: 'Response truncated for display. Check console for full response.'
            };
            if (responseData.analysis) {
                displayResponse.analysis = '... (truncated, see console)';
            }
        }

        responseDiv.textContent = `Response: ${JSON.stringify(displayResponse, null, 2)}`;
        detailsElement.appendChild(responseDiv);

        // Update timing
        const timingElement = requestItem.querySelector('.request-timing');
        timingElement.textContent = `Duration: ${(duration / 1000).toFixed(2)}s`;

        // Remove from active requests
        this.activeRequests.delete(requestId);

        // Auto-scroll to bottom
        this.requestList.scrollTop = this.requestList.scrollHeight;

        // Log to console for developer tools debugging
        console.group(`🔄 API Response #${requestId}: ${status.toUpperCase()}`);
        console.log('📄 File:', requestInfo.fileName);
        console.log('⏱️ Duration:', `${(duration / 1000).toFixed(2)}s`);
        if (status === 'success') {
            console.log('✅ Response Data:', responseData);
        } else {
            console.error('❌ Error Data:', responseData);
        }
        console.groupEnd();
    }

    splitDocumentIntoChunks(text, maxChunkSize = 45000) {
        const chunks = [];

        // Try to split by paragraphs first
        const paragraphs = text.split(/\n\s*\n/);
        let currentChunk = '';

        for (const paragraph of paragraphs) {
            // If adding this paragraph would exceed the limit
            if (currentChunk.length + paragraph.length + 2 > maxChunkSize) {
                if (currentChunk.length > 0) {
                    chunks.push(currentChunk.trim());
                    currentChunk = '';
                }

                // If single paragraph is too long, split by sentences
                if (paragraph.length > maxChunkSize) {
                    const sentences = paragraph.split(/[.!?]+\s+/);
                    let currentSentenceChunk = '';

                    for (const sentence of sentences) {
                        if (currentSentenceChunk.length + sentence.length + 2 > maxChunkSize) {
                            if (currentSentenceChunk.length > 0) {
                                chunks.push(currentSentenceChunk.trim());
                                currentSentenceChunk = '';
                            }

                            // If single sentence is still too long, force split
                            if (sentence.length > maxChunkSize) {
                                for (let i = 0; i < sentence.length; i += maxChunkSize) {
                                    chunks.push(sentence.substring(i, i + maxChunkSize));
                                }
                            } else {
                                currentSentenceChunk = sentence;
                            }
                        } else {
                            currentSentenceChunk += (currentSentenceChunk ? '. ' : '') + sentence;
                        }
                    }

                    if (currentSentenceChunk.length > 0) {
                        currentChunk = currentSentenceChunk;
                    }
                } else {
                    currentChunk = paragraph;
                }
            } else {
                currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
            }
        }

        if (currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
        }

        return chunks.length > 0 ? chunks : [text.substring(0, maxChunkSize)];
    }

    async downloadBatchResults() {
        if (!this.batchState || !this.batchState.results) {
            this.showStatus('No results to download', 'error');
            return;
        }

        const format = this.outputFormat.value;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `batch_results_${timestamp}.${format}`;

        try {
            let content, mimeType;

            if (format === 'json') {
                content = JSON.stringify(this.batchState.results, null, 2);
                mimeType = 'application/json';
            } else if (format === 'csv') {
                content = this.convertToCSV(this.batchState.results);
                mimeType = 'text/csv';
            } else if (format === 'xlsx') {
                // For Excel, we'll use JSON for now (would need a library for real Excel)
                content = JSON.stringify(this.batchState.results, null, 2);
                mimeType = 'application/json';
            }

            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showStatus(`Results downloaded as ${filename}`, 'success');
            this.logDebug(`Batch results downloaded: ${filename}`, 'info');

        } catch (error) {
            console.error('Error downloading results:', error);
            this.showStatus('Failed to download results: ' + error.message, 'error');
        }
    }

    convertToCSV(results) {
        const headers = ['File', 'Status', 'Processing Time (s)', 'Actors Found', 'Relationships', 'Error'];
        const rows = results.map(result => [
            result.file,
            result.status,
            (result.processingTime / 1000).toFixed(1),
            result.result?.analysis?.actors?.length || 0,
            result.result?.analysis?.relationships?.items?.length || 0,
            result.error || ''
        ]);

        return [headers, ...rows].map(row =>
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    viewFileResult(filename) {
        const result = this.batchState.results.find(r => r.file === filename);
        if (result && result.result) {
            // Display the result in the main analysis section
            this.displayResults(result.result);
            
            // Ensure raw responses are properly displayed
            if (result.result.analysis) {
                this.populateRawResponsesTab(result.result.analysis);
            }
            
            // Switch to the analysis section and results tab
            this.switchSection('analysis');
            this.switchTab('summary');
            
            this.showStatus(`Viewing results for ${filename}`, 'success');
        }
    }

    retryFile(filename) {
        // Find the original file and retry processing
        const file = this.selectedFiles.find(f => f.name === filename);
        if (file) {
            this.processFile(file).then(result => {
                // Update the result in the batch state
                const resultIndex = this.batchState.results.findIndex(r => r.file === filename);
                if (resultIndex !== -1) {
                    this.batchState.results[resultIndex] = {
                        file: filename,
                        status: 'success',
                        result: result,
                        processingTime: result.processingTime || 0
                    };
                    this.batchState.success++;
                    this.batchState.failed--;
                    this.populateBatchResults(Date.now() - this.batchState.startTime, 0);
                    this.showStatus(`Successfully retried ${filename}`, 'success');
                }
            }).catch(error => {
                this.showStatus(`Retry failed for ${filename}: ${error.message}`, 'error');
            });
        }
    }
}

// Make app globally available for onclick handlers
let app;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    app = new AdvancedDocumentAnalysisUI();
});